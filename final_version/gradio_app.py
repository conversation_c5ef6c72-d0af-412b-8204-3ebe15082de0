#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gradio 智能家居控制界面
"""

import gradio as gr
import sys
import os
import json
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入核心函数
try:
    from llm_controller import execute_nl, init_llm, preload_model
    print("✅ 成功导入核心控制模块")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    # 提供一个临时的测试函数
    def execute_nl(text):
        return {"message": f"收到指令: {text}", "status": "test_mode"}, []
    def preload_model():
        return False

def chat_interface(message, history):
    """
    聊天界面处理函数
    message: 用户当前输入
    history: 历史对话记录
    """
    if not message.strip():
        return "请输入控制指令"
    
    try:
        # 确保LLM已初始化（预加载失败的情况下）
        try:
            init_llm()
        except Exception as e:
            return f"❌ **LLM初始化失败**\n\n🔍 **错误信息：** {str(e)}\n\n请检查模型路径是否正确"
        
        # 调用核心函数
        result, resp = execute_nl(message)
        
        # 格式化响应
        response = f"✅ **指令执行成功！**\n\n"
        
        # 显示规范化后的指令
        response += f"📋 **执行的指令：**\n"
        response += f"- 设备: {result['entity_id']}\n"
        response += f"- 服务: {result['domain']}.{result['service']}\n"
        if result.get('args'):
            response += f"- 参数: {result['args']}\n"
        
        # 显示HA响应
        if resp:
            response += f"\n🔄 **设备响应：**\n"
            if isinstance(resp, list) and resp:
                entity_info = resp[0]
                response += f"- 状态: {entity_info.get('state', 'unknown')}\n"
                if 'attributes' in entity_info:
                    attrs = entity_info['attributes']
                    if 'brightness' in attrs:
                        response += f"- 亮度: {attrs['brightness']}\n"
                    if 'percentage' in attrs:
                        response += f"- 百分比: {attrs['percentage']}%\n"
                    if 'humidity' in attrs:
                        response += f"- 湿度: {attrs['humidity']}%\n"
            else:
                response += "- 命令已发送\n"
        
        return response
        
    except SystemExit as e:
        return f"❌ **执行失败**\n\n🔍 **错误信息：** {str(e)}"
    except Exception as e:
        error_details = traceback.format_exc()
        return f"❌ **系统错误**\n\n🔍 **错误信息：** {str(e)}\n\n📋 **详细信息：**\n```\n{error_details}\n```"

# 创建聊天界面
demo = gr.ChatInterface(
    fn=chat_interface,
    title="🏠 智能家居控制助手",
    description="""
    使用自然语言控制你的智能设备，支持以下设备类型：

    💡 **灯光控制** - 卧室灯、天花板灯、厨房灯
    🌀 **风扇控制** - 客厅风扇、吊扇
    🚪 **门窗控制** - 厨房窗户、客厅窗户、前门、厨房门
    🔧 **其他设备** - 加湿器、扫地机、警报器

    **示例指令：**
    - "把卧室灯开到70%"
    - "打开客厅风扇"
    - "关闭厨房窗户"
    - "锁上前门"
    - "加湿器设为55%"
    """,
    examples=[
        "把卧室灯开到70%",
        "打开客厅风扇",
        "关闭厨房窗户",
        "锁上前门",
        "加湿器设为55%",
        "打开警报器",
        "扫地机开始工作",
        "把天花板灯调到50%",
        "客厅风扇调到80%",
        "解锁前门"
    ],
    theme="soft"  # 简化参数，移除不支持的按钮配置
)

if __name__ == "__main__":
    print("🚀 启动智能家居控制助手...")
    print("📱 界面将在浏览器中自动打开")
    print("🌐 如需外部访问，请查看控制台输出的链接")

    # 检查模型文件是否存在
    model_path = "/home/<USER>/LLM_Models/Qwen2.5-0.5B-Instruct-GGUF/qwen2.5-0.5b-instruct-fp16.gguf"
    if not os.path.exists(model_path):
        print(f"⚠️  警告: 模型文件不存在: {model_path}")
        print("请检查模型路径是否正确")
    else:
        print(f"✅ 模型文件检查通过: {model_path}")

    # 预加载模型
    print("📦 预加载LLM模型...")
    if preload_model():
        print("✅ 模型预加载成功，首次响应将更快")
    else:
        print("⚠️ 模型预加载失败，将在首次使用时加载")

    try:
        demo.launch(
            server_name="0.0.0.0",    # 允许外部访问
            server_port=7860,         # 端口号
            share=False,              # 禁用公网链接避免错误
            inbrowser=False,          # 禁用自动打开浏览器
            show_error=True,          # 显示详细错误
            quiet=False               # 显示启动信息
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查端口7860是否被占用，或尝试修改端口号")
