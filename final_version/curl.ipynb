{"cells": [{"cell_type": "code", "execution_count": null, "id": "04fd6351", "metadata": {}, "outputs": [], "source": ["# ==== 基本配置（按需修改）====\n", "import os, json, requests, textwrap\n", "from datetime import datetime\n", "\n", "HA_URL   = os.getenv(\"HA_URL\",   \"http://**************:8123\").rstrip(\"/\")\n", "HA_TOKEN = os.getenv(\"HA_TOKEN\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2ZmI5MTgwMjQzYWM0MmVlYTAyMzMwZWM0YWZmYzEzYyIsImlhdCI6MTc1ODAwMzY0NywiZXhwIjoyMDczMzYzNjQ3fQ.RGPLPOkRo03xAaRgNF5dD_2KDuBa0UO9aaSbjp0X6DA\")  # 也可以 export HA_TOKEN=... 后不改这里\n", "\n", "TIMEOUT  = 15\n", "\n", "def _headers_json():\n", "    return {\"Authorization\": f\"Bearer {HA_TOKEN}\", \"Content-Type\": \"application/json\"}\n", "\n", "def _headers_auth():\n", "    return {\"Authorization\": f\"Bearer {HA_TOKEN}\"}\n", "\n", "def jprint(obj):\n", "    print(json.dumps(obj, ensure_ascii=False, indent=2))\n"]}, {"cell_type": "code", "execution_count": null, "id": "bc8ea9e3", "metadata": {}, "outputs": [], "source": ["def call_service(domain: str, service: str, entity_id: str, **kwargs):\n", "    \"\"\"调用 HA 服务。kwargs 会并入请求体。\"\"\"\n", "    url  = f\"{HA_URL}/api/services/{domain}/{service}\"\n", "    body = {\"entity_id\": entity_id}\n", "    body.update(kwargs or {})\n", "    r = requests.post(url, headers=_headers_json(), json=body, timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    try:\n", "        return r.json()\n", "    except ValueError:\n", "        return {\"ok\": True, \"status_code\": r.status_code, \"text\": r.text}\n", "\n", "def get_state(entity_id: str):\n", "    \"\"\"读取单个实体状态。\"\"\"\n", "    url = f\"{HA_URL}/api/states/{entity_id}\"\n", "    r = requests.get(url, headers=_headers_auth(), timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    return r.json()\n", "\n", "def list_states(prefix: str | None = None):\n", "    \"\"\"列出全部实体，或按 domain 前缀过滤（如 'light.'、'fan.'）。\"\"\"\n", "    url = f\"{HA_URL}/api/states\"\n", "    r = requests.get(url, headers=_headers_auth(), timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    data = r.json()\n", "    if prefix:\n", "        data = [s for s in data if s[\"entity_id\"].startswith(prefix)]\n", "    return data\n", "\n", "def curl_equiv(domain: str, service: str, entity_id: str, **kwargs):\n", "    \"\"\"打印等价的 curl 命令，便于调试。\"\"\"\n", "    url  = f\"{HA_URL}/api/services/{domain}/{service}\"\n", "    body = {\"entity_id\": entity_id}; body.update(kwargs or {})\n", "    curl = (\n", "        \"curl -X POST \"\n", "        f\"'{url}' -H 'Content-Type: application/json' \"\n", "        \"-H 'Authorization: Bearer $HA_TOKEN' \"\n", "        f\"-d '{json.dumps(body, ensure_ascii=False)}'\"\n", "    )\n", "    print(curl)\n"]}, {"cell_type": "code", "execution_count": null, "id": "5dd7c9eb", "metadata": {}, "outputs": [], "source": ["def run_cmd(cmd: dict, check_state: bool = True):\n", "    \"\"\"\n", "    cmd 形如：\n", "      {\"domain\":\"light\",\"service\":\"turn_on\",\"entity_id\":\"light.bed_light\",\"args\":{\"brightness_pct\":80}}\n", "    \"\"\"\n", "    domain    = cmd[\"domain\"]\n", "    service   = cmd[\"service\"]\n", "    entity_id = cmd[\"entity_id\"]\n", "    args      = cmd.get(\"args\") or {}\n", "\n", "    # 安全小兜底：domain != entity_id 前缀时，强制更正\n", "    prefix = entity_id.split(\".\", 1)[0]\n", "    if prefix != domain:\n", "        print(f\"[WARN] domain={domain} 与 entity_id 前缀={prefix} 不一致，已使用前缀覆盖。\")\n", "        domain = prefix\n", "\n", "    print(\"=== 等价 curl ===\")\n", "    curl_equiv(domain, service, entity_id, **args)\n", "\n", "    print(\"\\n=== 执行服务 ===\")\n", "    resp = call_service(domain, service, entity_id, **args)\n", "    jprint(resp if resp else {\"ok\": True, \"note\": \"服务已调用，未返回 JSON\"})\n", "\n", "    if check_state:\n", "        try:\n", "            st = get_state(entity_id)\n", "            print(\"\\n=== 最新状态 ===\")\n", "            jprint(st)\n", "        except Exception as e:\n", "            print(f\"[WARN] 读取状态失败：{e}\")\n", "\n", "    return resp\n"]}, {"cell_type": "code", "execution_count": null, "id": "dd5536d1", "metadata": {}, "outputs": [], "source": ["# ---- Light ----\n", "def light_on(entity_id, brightness_pct=None, color_temp_kelvin=None):\n", "    args = {}\n", "    if brightness_pct is not None: args[\"brightness_pct\"] = int(brightness_pct)\n", "    if color_temp_kelvin is not None: args[\"color_temp_kelvin\"] = int(color_temp_kelvin)\n", "    return call_service(\"light\", \"turn_on\", entity_id, **args)\n", "\n", "def light_off(entity_id):\n", "    return call_service(\"light\", \"turn_off\", entity_id)\n", "\n", "# ---- Fan ----\n", "def fan_on(entity_id):  return call_service(\"fan\", \"turn_on\", entity_id)\n", "def fan_off(entity_id): return call_service(\"fan\", \"turn_off\", entity_id)\n", "def fan_set_percentage(entity_id, percentage: int):\n", "    return call_service(\"fan\", \"set_percentage\", entity_id, percentage=int(percentage))\n", "\n", "# ---- Cover ----\n", "def cover_open(entity_id):  return call_service(\"cover\", \"open_cover\", entity_id)\n", "def cover_close(entity_id): return call_service(\"cover\", \"close_cover\", entity_id)\n", "def cover_stop(entity_id):  return call_service(\"cover\", \"stop_cover\", entity_id)\n", "\n", "# ---- Lock ----\n", "def lock_lock(entity_id):   return call_service(\"lock\", \"lock\", entity_id)\n", "def lock_unlock(entity_id): return call_service(\"lock\", \"unlock\", entity_id)\n", "\n", "# ---- Humidifier ----\n", "def humidifier_on(entity_id):  return call_service(\"humidifier\",\"turn_on\", entity_id)\n", "def humidifier_off(entity_id): return call_service(\"humidifier\",\"turn_off\", entity_id)\n", "def humidifier_set(entity_id, humidity: int):\n", "    return call_service(\"humidifier\",\"set_humidity\", entity_id, humidity=int(humidity))\n", "\n", "# ---- Vacuum ----\n", "def vacuum_start(entity_id):       return call_service(\"vacuum\",\"start\",entity_id)\n", "def vacuum_pause(entity_id):       return call_service(\"vacuum\",\"pause\",entity_id)\n", "def vacuum_stop(entity_id):        return call_service(\"vacuum\",\"stop\",entity_id)\n", "def vacuum_return(entity_id):      return call_service(\"vacuum\",\"return_to_base\",entity_id)\n", "def vacuum_set_fan(entity_id, speed: str):\n", "    return call_service(\"vacuum\",\"set_fan_speed\", entity_id, fan_speed=str(speed))\n", "\n", "# ---- Siren ----\n", "def siren_on(entity_id, tone=None, volume_level=None):\n", "    args = {}\n", "    if tone is not None: args[\"tone\"] = str(tone)\n", "    if volume_level is not None: args[\"volume_level\"] = float(volume_level)\n", "    return call_service(\"siren\",\"turn_on\", entity_id, **args)\n", "\n", "def siren_off(entity_id): return call_service(\"siren\",\"turn_off\", entity_id)\n"]}, {"cell_type": "code", "execution_count": null, "id": "d03ffc0a", "metadata": {}, "outputs": [], "source": ["# 按你的 ha_index.json 整理\n", "LIGHT_BED        = \"light.bed_light\"\n", "LIGHT_CEILING    = \"light.ceiling_lights\"\n", "LIGHT_KITCHEN    = \"light.kitchen_lights\"\n", "\n", "FAN_CEILING      = \"fan.ceiling_fan\"\n", "FAN_LIVING       = \"fan.living_room_fan\"\n", "\n", "COVER_KITCHEN    = \"cover.kitchen_window\"\n", "COVER_LIVING     = \"cover.living_room_window\"\n", "\n", "LOCK_FRONT       = \"lock.front_door\"\n", "LOCK_KITCHEN     = \"lock.kitchen_door\"\n", "\n", "HUMIDIFIER       = \"humidifier.humidifier\"\n", "\n", "VACUUM_GROUND    = \"vacuum.demo_vacuum_0_ground_floor\"\n", "\n", "SIREN_MAIN       = \"siren.siren\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "a023e5e3", "metadata": {}, "outputs": [], "source": ["# # 1) 开灯并调亮度\n", "light_on(LIGHT_BED, brightness_pct=80)\n", "\n", "# 2) 风扇 70%\n", "fan_set_percentage(FAN_CEILING, 70)\n", "\n", "cover_open(COVER_KITCHEN)\n", "\n", "# 4) 门锁解锁 / 上锁\n", "lock_unlock(LOCK_FRONT)\n", "lock_lock(LOCK_FRONT)\n", "\n", "# 5) 加湿器设为 55%\n", "humidifier_set(HUMIDIFIER, 55)\n", "\n", "# 6) 扫地机开始 → 调风速 high → 回充\n", "vacuum_start(VACUUM_GROUND)\n", "vacuum_set_fan(VACUUM_GROUND, \"high\")\n", "vacuum_return(VACUUM_GROUND)\n", "\n", "# 7) 警报器开（可选 tone/音量）→ 关\n", "siren_on(SIREN_MAIN)   # 或 siren_on(SIREN_MAIN, tone=\"fire\", volume_level=1.0)\n", "siren_off(SIREN_MAIN)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "llamacpp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}