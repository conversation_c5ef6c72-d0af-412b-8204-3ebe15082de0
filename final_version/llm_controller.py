#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM 控制器 - 处理自然语言到HA命令的转换
"""

import json
import re
import time
import psutil
import os
from llama_cpp import Llama, LlamaGrammar
from ha_controller import (
    HA_INDEX, DOMAIN_MAP, SERVICE_MAP, ALLOWED_DOMAINS,
    INTENT_ALIAS_HINTS, _remap_args, resolve_entity_id_smart,
    call_service, get_state, jprint, build_entity_hints, HA_URL
)

# ==== LLM 配置 ====
MODEL_PATH = "/home/<USER>/LLM_Models/Qwen2.5-0.5B-Instruct-GGUF/qwen2.5-0.5b-instruct-fp16.gguf"

# 全局LLM实例
llm = None

def get_optimal_threads():
    """根据CPU核心数确定最佳线程数"""
    try:
        cpu_count = psutil.cpu_count(logical=False)  # 物理核心
        # ARM64通常超线程效果不好，使用物理核心数
        return min(cpu_count or 4, 6)  # 最多6线程，避免过度竞争
    except:
        return 4  # 默认4线程

def get_optimal_context():
    """根据内存大小确定最佳context长度"""
    try:
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 4:
            return 1024  # 低内存设备
        elif memory_gb < 8:
            return 1536
        else:
            return 2048
    except:
        return 2048  # 默认值

def init_llm():
    """优化的LLM初始化，针对ARM64优化"""
    global llm
    if llm is None:
        print("🔄 正在加载LLM模型...")
        start_time = time.time()

        # ARM64优化参数
        optimal_threads = get_optimal_threads()
        optimal_context = get_optimal_context()

        print(f"📊 使用 {optimal_threads} 线程，context={optimal_context}")

        llm = Llama(
            model_path=MODEL_PATH,
            n_threads=optimal_threads,      # 优化线程数
            n_ctx=optimal_context,          # 根据内存调整
            use_mmap=True,                  # 使用内存映射减少内存使用
            use_mlock=False,                # ARM64上可能不需要
            n_batch=256,                    # 减小batch size
            verbose=False                   # 减少输出
        )

        load_time = time.time() - start_time
        print(f"✅ 模型加载完成，耗时 {load_time:.2f} 秒")

    return llm

def preload_model():
    """应用启动时预加载模型"""
    try:
        init_llm()
        return True
    except Exception as e:
        print(f"❌ 模型预加载失败: {e}")
        return False

# JSON语法约束
GBNF_JSON = r'''
ws ::= [ \t\n\r]*
root ::= ws object ws
object ::= "{" ws pair (ws "," ws pair)* ws "}"
pair ::= string ws ":" ws value
string ::= "\"" [^\"]* "\""
value ::= string | number | object | array | "true" | "false" | "null"
array ::= "[" ws (value (ws "," ws value)*)? ws "]"
number ::= "-"? [0-9]+ ("." [0-9]+)?
'''

def get_grammar():
    """获取JSON语法约束"""
    return LlamaGrammar.from_string(GBNF_JSON)

def format_chat_qwen(messages):
    """格式化Qwen聊天模板"""
    out = []
    for m in messages:
        out.append(f"<|im_start|>{m['role']}\n{m['content']}<|im_end|>\n")
    out.append("<|im_start|>assistant\n")
    return "".join(out)

# 系统提示词
SYSTEM_PROMPT = (
    "你是 Home Assistant 控制器，只能输出 JSON："
    '{"domain":"...","service":"...","entity_id":"...","args":{...}}。\n'
    "实体提示：\n" + build_entity_hints(HA_INDEX)
)

def llm_parse_to_json(user_text: str):
    """使用LLM解析自然语言为JSON命令"""
    llm_instance = init_llm()
    grammar = get_grammar()
    
    prompt = format_chat_qwen([
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": user_text}
    ])
    
    out = llm_instance.create_completion(
        prompt=prompt, 
        max_tokens=128, 
        temperature=0, 
        grammar=grammar, 
        stop=["<|im_end|>"]
    )
    
    raw = out["choices"][0]["text"].strip()
    print("LLM RAW:", raw)
    return json.loads(raw)

def normalize_cmd_with_text_v2(cmd: dict, user_text: str | None = None) -> dict:
    """
    重构版本：更简单、更可靠的命令规范化
    """
    raw_domain = (cmd.get("domain") or "").strip().lower()
    raw_service = (cmd.get("service") or "").strip().lower()
    raw_eid = (cmd.get("entity_id") or "").strip()
    raw_args = cmd.get("args") or {}
    
    print(f"🔍 输入: domain={raw_domain}, service={raw_service}, entity_id={raw_eid}")
    
    # 1. 实体解析 - 简化逻辑，优先使用关键词匹配
    eid = None
    
    # 1.1 先尝试直接匹配（如果是正确的entity_id）
    if raw_eid and "." in raw_eid:
        entity_key = raw_eid.lower()
        if entity_key in HA_INDEX["by_entity"]:
            eid = entity_key
            print(f"🔍 直接匹配成功: {eid}")
    
    # 1.2 如果直接匹配失败，使用关键词兜底
    if not eid and user_text:
        ut = user_text.lower()
        print(f"🔍 关键词匹配，用户输入: {ut}")
        
        # 优先匹配中文关键词
        for keywords, candidate in INTENT_ALIAS_HINTS:
            if isinstance(keywords, str):
                keywords = (keywords,)
            for keyword in keywords:
                if keyword.lower() in ut:
                    eid = candidate
                    print(f"🔍 关键词匹配成功: '{keyword}' -> {eid}")
                    break
            if eid:
                break
    
    # 1.3 最后尝试by_name匹配
    if not eid:
        for name, candidates in HA_INDEX["by_name"].items():
            if name.lower() in (user_text or "").lower():
                eid = candidates[0]
                print(f"🔍 by_name匹配成功: '{name}' -> {eid}")
                break
    
    if not eid:
        raise SystemExit(f"无法解析实体: {raw_eid}, 用户输入: {user_text}")
    
    # 2. 确定正确的domain（基于entity_id前缀）
    true_domain = eid.split(".", 1)[0]
    print(f"🔍 真实domain: {true_domain}")
    
    # 3. 服务映射（基于真实domain）
    service_map = SERVICE_MAP.get(true_domain, {})
    service = service_map.get(raw_service, raw_service)
    print(f"🔍 服务映射: {raw_service} -> {service} (domain: {true_domain})")
    
    # 4. 如果服务还是不对，根据用户意图强制修正
    meta = HA_INDEX["by_entity"].get(eid, {})
    allowed_services = set(meta.get("services", []))

    if service not in allowed_services:
        print(f"🔍 服务不在白名单，尝试智能修正...")
        ut = (user_text or "").lower()
        
        # 通用的开关类设备
        if "turn_on" in allowed_services and "turn_off" in allowed_services:
            if any(k in ut for k in ["关", "关闭", "off", "停止"]):
                service = "turn_off"
            else:
                service = "turn_on"  # 默认开启
        
        # 特殊设备的特殊处理
        elif true_domain == "cover":
            if any(k in ut for k in ["开", "打开", "open"]):
                service = "open_cover"
            elif any(k in ut for k in ["关", "关闭", "close"]):
                service = "close_cover"
            else:
                service = "open_cover"
                
        elif true_domain == "fan":
            if "%" in ut or "百分比" in ut or any(k in raw_args for k in ["percentage", "value"]):
                service = "set_percentage"
            elif "turn_off" in allowed_services:
                service = "turn_off"
            else:
                service = "turn_on"
                
        elif true_domain == "lock":
            if any(k in ut for k in ["解锁", "unlock", "开锁"]):
                service = "unlock"
            else:
                service = "lock"
                
        elif true_domain == "humidifier":
            if "%" in ut or "湿度" in ut:
                service = "set_humidity"
            elif "turn_off" in allowed_services:
                service = "turn_off"
            else:
                service = "turn_on"
                
        elif true_domain == "vacuum":
            if any(k in ut for k in ["风速", "speed", "fan"]):
                service = "set_fan_speed"
            elif any(k in ut for k in ["暂停", "pause"]):
                service = "pause"
            elif any(k in ut for k in ["停止", "stop"]):
                service = "stop"
            elif any(k in ut for k in ["回充", "return", "dock"]):
                service = "return_to_base"
            else:
                service = "start"
        
        print(f"🔍 智能修正后服务: {service}")
    
    # 5. 最终验证
    if service not in allowed_services:
        raise SystemExit(f"服务不被允许: {true_domain}.{service} for {eid}; 允许: {sorted(allowed_services)}")
    
    # 6. 参数处理
    args = _remap_args(true_domain, service, raw_args)
    
    # 参数白名单过滤
    allowed_args = set(meta.get("args", {}).get(service, []))
    args = {k: v for k, v in args.items() if k in allowed_args}
    
    # 数值范围处理
    if true_domain == "light" and service == "turn_on" and "brightness_pct" in args:
        try:
            args["brightness_pct"] = max(0, min(100, int(args["brightness_pct"])))
        except:
            args.pop("brightness_pct", None)
    
    if true_domain == "fan" and service == "set_percentage" and "percentage" in args:
        try:
            args["percentage"] = max(0, min(100, int(args["percentage"])))
        except:
            raise SystemExit("fan.set_percentage 需要整数 percentage 0-100")

    if true_domain == "humidifier" and service == "set_humidity" and "humidity" in args:
        try:
            args["humidity"] = max(0, min(100, int(args["humidity"])))
        except:
            raise SystemExit("humidifier.set_humidity 需要整数 humidity 0-100")

    result = {"domain": true_domain, "service": service, "entity_id": eid, "args": args}
    print(f"🔍 最终结果: {result}")
    return result

def execute_nl(user_text: str, check_state: bool = True):
    """执行自然语言指令"""
    cmd_raw = llm_parse_to_json(user_text)
    fixed = normalize_cmd_with_text_v2(cmd_raw, user_text=user_text)

    print("=== 规范化指令 ===")
    jprint(fixed)

    # 打印等价 curl
    body = {"entity_id": fixed["entity_id"]}
    body.update(fixed.get("args") or {})
    curl = (
        f"curl -X POST '{HA_URL}/api/services/{fixed['domain']}/{fixed['service']}' "
        "-H 'Content-Type: application/json' "
        "-H 'Authorization: Bearer $HA_TOKEN' "
        f"-d '{json.dumps(body, ensure_ascii=False)}'"
    )
    print("\n=== 等价 curl ===")
    print(curl)

    # 真正执行
    resp = call_service(fixed["domain"], fixed["service"], fixed["entity_id"], **(fixed.get("args") or {}))
    try:
        print("\n=== 返回 ===")
        jprint(resp)
    except Exception:
        print(resp)

    if check_state:
        try:
            st = get_state(fixed["entity_id"])
            print("\n=== 最新状态 ===")
            jprint(st)
        except Exception as e:
            print(f"[WARN] 读取状态失败：{e}")

    return fixed, resp
