#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Home Assistant 智能控制器
从 llm_new.ipynb 提取的核心功能
"""

import os
import json
import requests
import textwrap
import re
from datetime import datetime
from llama_cpp import Llama, LlamaGrammar

# ==== 基本配置 ====
HA_URL = os.getenv("HA_URL", "http://**************:8123").rstrip("/")
HA_TOKEN = os.getenv("HA_TOKEN", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2ZmI5MTgwMjQzYWM0MmVlYTAyMzMwZWM0YWZmYzEzYyIsImlhdCI6MTc1ODAwMzY0NywiZXhwIjoyMDczMzYzNjQ3fQ.RGPLPOkRo03xAaRgNF5dD_2KDuBa0UO9aaSbjp0X6DA")
TIMEOUT = 15

def _headers_json():
    return {"Authorization": f"Bearer {HA_TOKEN}", "Content-Type": "application/json"}

def _headers_auth():
    return {"Authorization": f"Bearer {HA_TOKEN}"}

def jprint(obj):
    print(json.dumps(obj, ensure_ascii=False, indent=2))

# ==== HA API 调用函数 ====
def call_service(domain: str, service: str, entity_id: str, **kwargs):
    url = f"{HA_URL}/api/services/{domain}/{service}"
    body = {"entity_id": entity_id}
    body.update(kwargs or {})
    r = requests.post(url, headers=_headers_json(), json=body, timeout=TIMEOUT)
    r.raise_for_status()
    try:
        return r.json()
    except ValueError:
        return {"ok": True, "status_code": r.status_code, "text": r.text}

def get_state(entity_id: str):
    url = f"{HA_URL}/api/states/{entity_id}"
    r = requests.get(url, headers=_headers_auth(), timeout=TIMEOUT)
    r.raise_for_status()
    return r.json()

# ==== 设备配置 ====
HA_INDEX = {
    "by_entity": {
        "humidifier.humidifier": {
            "friendly_name": "Humidifier",
            "domain": "humidifier",
            "services": ["turn_on", "turn_off", "set_humidity"],
            "args": {"set_humidity": ["humidity"]}
        },
        "light.bed_light": {
            "friendly_name": "Bed Light",
            "domain": "light",
            "services": ["turn_on", "turn_off"],
            "args": {"turn_on": ["brightness_pct", "color_temp_kelvin"]}
        },
        "light.ceiling_lights": {
            "friendly_name": "Ceiling Lights",
            "domain": "light",
            "services": ["turn_on", "turn_off"],
            "args": {"turn_on": ["brightness_pct"]}
        },
        "light.kitchen_lights": {
            "friendly_name": "Kitchen Lights",
            "domain": "light",
            "services": ["turn_on", "turn_off"],
            "args": {"turn_on": ["brightness_pct"]}
        },
        "fan.ceiling_fan": {
            "friendly_name": "Ceiling Fan",
            "domain": "fan",
            "services": ["turn_on", "turn_off", "set_percentage"],
            "args": {"set_percentage": ["percentage"]}
        },
        "fan.living_room_fan": {
            "friendly_name": "Living Room Fan",
            "domain": "fan",
            "services": ["turn_on", "turn_off", "set_percentage"],
            "args": {"set_percentage": ["percentage"]}
        },
        "vacuum.demo_vacuum_0_ground_floor": {
            "friendly_name": "Demo vacuum 0 ground floor",
            "domain": "vacuum",
            "services": ["start", "pause", "stop", "return_to_base", "set_fan_speed"],
            "args": {"set_fan_speed": ["fan_speed"]}
        },
        "lock.front_door": {
            "friendly_name": "Front Door",
            "domain": "lock",
            "services": ["lock", "unlock"]
        },
        "lock.kitchen_door": {
            "friendly_name": "Kitchen Door",
            "domain": "lock",
            "services": ["lock", "unlock"]
        },
        "cover.kitchen_window": {
            "friendly_name": "Kitchen Window",
            "domain": "cover",
            "services": ["open_cover", "close_cover", "stop_cover"]
        },
        "cover.living_room_window": {
            "friendly_name": "Living Room Window",
            "domain": "cover",
            "services": ["open_cover", "close_cover", "stop_cover"]
        },
        "siren.siren": {
            "friendly_name": "Siren",
            "domain": "siren",
            "services": ["turn_on", "turn_off"],
            "args": {"turn_on": ["tone", "volume_level"]}
        }
    },
    "by_name": {
        "humidifier": ["humidifier.humidifier"],
        "bed light": ["light.bed_light"],
        "ceiling lights": ["light.ceiling_lights"],
        "kitchen lights": ["light.kitchen_lights"],
        "ceiling fan": ["fan.ceiling_fan"],
        "living room fan": ["fan.living_room_fan"],
        "vacuum": ["vacuum.demo_vacuum_0_ground_floor"],
        "front door": ["lock.front_door"],
        "kitchen door": ["lock.kitchen_door"],
        "kitchen window": ["cover.kitchen_window"],
        "living room window": ["cover.living_room_window"],
        "siren": ["siren.siren"]
    }
}

# 中文别名
BY_NAME_CN = {
    "卧室灯": ["light.bed_light"],
    "天花板灯": ["light.ceiling_lights"],
    "厨房灯": ["light.kitchen_lights"],
    "客厅风扇": ["fan.living_room_fan"],
    "吊扇": ["fan.ceiling_fan"],
    "厨房窗户": ["cover.kitchen_window"],
    "客厅窗户": ["cover.living_room_window"],
    "前门": ["lock.front_door"],
    "厨房门": ["lock.kitchen_door"],
    "扫地机": ["vacuum.demo_vacuum_0_ground_floor"],
    "加湿器": ["humidifier.humidifier"],
    "警报器": ["siren.siren"],
}
HA_INDEX["by_name"].update(BY_NAME_CN)

def build_entity_hints(ha_index: dict) -> str:
    lines = []
    for alias, eids in ha_index.get("by_name", {}).items():
        eids_s = ", ".join(eids)
        lines.append(f"- {alias} => {eids_s}")
    return "\n".join(lines)

# ==== 映射配置 ====
DOMAIN_MAP = {
    "lights": "light",
    "fans": "fan",
    "homeassistant": "",
    "home": "",
    "humidity": "humidifier",
    "sirens": "siren",
    "covers": "cover",
    "locks": "lock",
    "vacuums": "vacuum",
}

SERVICE_MAP = {
    "light": {
        "set_brightness": "turn_on",
        "turn_on": "turn_on",
        "turn_off": "turn_off",
        "set_light": "turn_on",
        "set": "turn_on",
    },
    "fan": {
        "set_speed": "set_percentage",
        "set_percentage": "set_percentage",
        "turn_on": "turn_on",
        "turn_off": "turn_off",
        "set": "set_percentage",
    },
    "cover": {
        "open": "open_cover",
        "close": "close_cover",
        "stop": "stop_cover"
    },
    "lock": {
        "lock": "lock",
        "unlock": "unlock",
        "lock.front_door": "lock",
        "unlock.front_door": "unlock",
    },
    "humidifier": {
        "set_humidity": "set_humidity",
        "turn_on": "turn_on",
        "turn_off": "turn_off",
        "set": "set_humidity",
    },
    "vacuum": {
        "start": "start",
        "pause": "pause",
        "stop": "stop",
        "return": "return_to_base",
        "set_fan_speed": "set_fan_speed",
        "dock": "return_to_base",
    },
    "siren": {
        "turn_on": "turn_on",
        "turn_off": "turn_off"
    }
}

ALLOWED_DOMAINS = {"light", "fan", "cover", "lock", "humidifier", "vacuum", "siren", "sensor"}

# 关键字提示
INTENT_ALIAS_HINTS = [
    (("厨房窗", "厨房的窗", "厨房窗户", "开厨房窗", "打开厨房窗", "打开厨房的窗", "打开厨房窗户", "kitchen window"),
     "cover.kitchen_window"),
    (("客厅窗", "客厅的窗", "客厅窗户", "开客厅窗", "打开客厅窗", "打开客厅的窗", "打开客厅窗户", "living room window"),
     "cover.living_room_window"),
    (("卧室灯", "bed", "卧室"), "light.bed_light"),
    (("天花板灯", "顶灯", "ceiling"), "light.ceiling_lights"),
    (("厨房灯", "厨房"), "light.kitchen_lights"),
    (("客厅风扇", "客厅风"), "fan.living_room_fan"),
    (("吊扇",), "fan.ceiling_fan"),
    (("前门", "front door"), "lock.front_door"),
    (("厨房门", "kitchen door"), "lock.kitchen_door"),
    (("加湿器", "humidifier"), "humidifier.humidifier"),
    (("扫地机", "vacuum"), "vacuum.demo_vacuum_0_ground_floor"),
    (("警报器", "siren", "报警"), "siren.siren"),
]

# ==== 参数映射函数 ====
def _remap_args(domain: str, service: str, raw_args: dict) -> dict:
    """参数重映射"""
    args = {}

    for k, v in raw_args.items():
        # 通用映射
        if k in ("value", "level", "percentage_value", "percent"):
            if domain == "light" and service == "turn_on":
                args["brightness_pct"] = v
            elif domain == "fan" and service == "set_percentage":
                args["percentage"] = v
            elif domain == "humidifier" and service == "set_humidity":
                args["humidity"] = v
            else:
                args[k] = v
        elif k in ("humidity_pct", "humidity_percentage"):
            args["humidity"] = v
        elif k in ("brightness_pct", "brightness_percentage", "brightness"):
            args["brightness_pct"] = v
        elif k in ("fan_speed", "speed"):
            if domain == "vacuum":
                args["fan_speed"] = v
            elif domain == "fan":
                args["percentage"] = v  # 风扇速度转换为百分比
            else:
                args[k] = v
        else:
            args[k] = v

    return args

# ==== 实体解析函数 ====
def resolve_entity_id(text: str) -> str | None:
    text_norm = text.strip().lower()

    if "." in text_norm and text_norm in HA_INDEX["by_entity"]:
        return text_norm
    if text_norm in HA_INDEX["by_name"]:
        return HA_INDEX["by_name"][text_norm][0]
    for eid, meta in HA_INDEX["by_entity"].items():
        if text_norm in meta.get("friendly_name", "").lower():
            return eid
    return None

def resolve_entity_id_smart(entity_text: str, user_text: str | None = None) -> str | None:
    """
    先用原来的 resolve_entity_id；若失败，再用 user_text 里的关键词兜底。
    """
    eid = resolve_entity_id(entity_text) if entity_text else None
    if eid:
        return eid

    ut = (user_text or "").lower()
    for keywords, candidate in INTENT_ALIAS_HINTS:
        if isinstance(keywords, str):
            keywords = (keywords,)
        if any(k.lower() in ut for k in keywords):
            return candidate
    return None
