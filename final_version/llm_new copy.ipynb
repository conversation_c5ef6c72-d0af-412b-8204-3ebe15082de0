{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a3d4b86e", "metadata": {}, "outputs": [], "source": ["# ==== 基本配置（按需修改）====\n", "import os, json, requests, textwrap, re\n", "from datetime import datetime\n", "from llama_cpp import Llama, LlamaGrammar\n", "\n", "HA_URL   = os.getenv(\"HA_URL\",   \"http://**************:8123\").rstrip(\"/\")\n", "HA_TOKEN = os.getenv(\"HA_TOKEN\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2ZmI5MTgwMjQzYWM0MmVlYTAyMzMwZWM0YWZmYzEzYyIsImlhdCI6MTc1ODAwMzY0NywiZXhwIjoyMDczMzYzNjQ3fQ.RGPLPOkRo03xAaRgNF5dD_2KDuBa0UO9aaSbjp0X6DA\")  # 也可以 export HA_TOKEN=... 后不改这里\n", "\n", "TIMEOUT  = 15\n", "\n", "def _headers_json():\n", "    return {\"Authorization\": f\"Bearer {HA_TOKEN}\", \"Content-Type\": \"application/json\"}\n", "\n", "def _headers_auth():\n", "    return {\"Authorization\": f\"Bearer {HA_TOKEN}\"}\n", "\n", "def jprint(obj):\n", "    print(json.dumps(obj, ensure_ascii=False, indent=2))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "bc52aba5", "metadata": {}, "outputs": [], "source": ["def call_service(domain: str, service: str, entity_id: str, **kwargs):\n", "    url  = f\"{HA_URL}/api/services/{domain}/{service}\"\n", "    body = {\"entity_id\": entity_id}; body.update(kwargs or {})\n", "    r = requests.post(url, headers=_headers_json(), json=body, timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    try:\n", "        return r.json()\n", "    except ValueError:\n", "        return {\"ok\": True, \"status_code\": r.status_code, \"text\": r.text}\n", "\n", "def get_state(entity_id: str):\n", "    url = f\"{HA_URL}/api/states/{entity_id}\"\n", "    r = requests.get(url, headers=_headers_auth(), timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    return r.json()\n"]}, {"cell_type": "code", "execution_count": 3, "id": "95dffb97", "metadata": {}, "outputs": [], "source": ["HA_INDEX = {\n", "  \"by_entity\": {\n", "    \"humidifier.humidifier\": {\n", "      \"friendly_name\": \"Humidifier\",\n", "      \"domain\": \"humidifier\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_humidity\"],\n", "      \"args\": { \"set_humidity\": [\"humidity\"] }\n", "    },\n", "    \"light.bed_light\": {\n", "      \"friendly_name\": \"Bed Light\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\", \"color_temp_kelvin\"] }\n", "    },\n", "    \"light.ceiling_lights\": {\n", "      \"friendly_name\": \"Ceiling Lights\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\"] }\n", "    },\n", "    \"light.kitchen_lights\": {\n", "      \"friendly_name\": \"Kitchen Lights\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\"] }\n", "    },\n", "    \"fan.ceiling_fan\": {\n", "      \"friendly_name\": \"Ceiling Fan\",\n", "      \"domain\": \"fan\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_percentage\"],\n", "      \"args\": { \"set_percentage\": [\"percentage\"] }\n", "    },\n", "    \"fan.living_room_fan\": {\n", "      \"friendly_name\": \"Living Room Fan\",\n", "      \"domain\": \"fan\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_percentage\"],\n", "      \"args\": { \"set_percentage\": [\"percentage\"] }\n", "    },\n", "    \"vacuum.demo_vacuum_0_ground_floor\": {\n", "      \"friendly_name\": \"Demo vacuum 0 ground floor\",\n", "      \"domain\": \"vacuum\",\n", "      \"services\": [\"start\", \"pause\", \"stop\", \"return_to_base\", \"set_fan_speed\"],\n", "      \"args\": { \"set_fan_speed\": [\"fan_speed\"] }\n", "    },\n", "    \"lock.front_door\": {\n", "      \"friendly_name\": \"Front Door\",\n", "      \"domain\": \"lock\",\n", "      \"services\": [\"lock\", \"unlock\"]\n", "    },\n", "    \"lock.kitchen_door\": {\n", "      \"friendly_name\": \"Kitchen Door\",\n", "      \"domain\": \"lock\",\n", "      \"services\": [\"lock\", \"unlock\"]\n", "    },\n", "    # \"sensor.outside_temperature\": {\n", "    #   \"friendly_name\": \"Outside Temperature\",\n", "    #   \"domain\": \"sensor\",\n", "    #   \"services\": []\n", "    # },\n", "    # \"sensor.carbon_dioxide\": {\n", "    #   \"friendly_name\": \"Carbon Dioxide\",\n", "    #   \"domain\": \"sensor\",\n", "    #   \"services\": []\n", "    # },\n", "    \"cover.kitchen_window\": {\n", "      \"friendly_name\": \"Kitchen Window\",\n", "      \"domain\": \"cover\",\n", "      \"services\": [\"open_cover\", \"close_cover\", \"stop_cover\"]\n", "    },\n", "    \"cover.living_room_window\": {\n", "      \"friendly_name\": \"Living Room Window\",\n", "      \"domain\": \"cover\",\n", "      \"services\": [\"open_cover\", \"close_cover\", \"stop_cover\"]\n", "    },\n", "    \"siren.siren\": {\n", "      \"friendly_name\": \"<PERSON><PERSON>\",\n", "      \"domain\": \"siren\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"tone\", \"volume_level\"] }\n", "    }\n", "  },\n", "  \"by_name\": {\n", "    \"humidifier\": [\"humidifier.humidifier\"],\n", "    \"bed light\": [\"light.bed_light\"],\n", "    \"ceiling lights\": [\"light.ceiling_lights\"],\n", "    \"kitchen lights\": [\"light.kitchen_lights\"],\n", "    \"ceiling fan\": [\"fan.ceiling_fan\"],\n", "    \"living room fan\": [\"fan.living_room_fan\"],\n", "    \"vacuum\": [\"vacuum.demo_vacuum_0_ground_floor\"],\n", "    \"front door\": [\"lock.front_door\"],\n", "    \"kitchen door\": [\"lock.kitchen_door\"],\n", "    # \"outside temperature\": [\"sensor.outside_temperature\"],\n", "    # \"carbon dioxide\": [\"sensor.carbon_dioxide\"],\n", "    \"kitchen window\": [\"cover.kitchen_window\"],\n", "    \"living room window\": [\"cover.living_room_window\"],\n", "    \"siren\": [\"siren.siren\"]\n", "  }\n", "}\n"]}, {"cell_type": "code", "execution_count": 4, "id": "112c0313", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- humidifier => humidifier.humidifier\n", "- bed light => light.bed_light\n", "- ceiling lights => light.ceiling_lights\n", "- kitchen lights => light.kitchen_lights\n", "- ceiling fan => fan.ceiling_fan\n", "- living room fan => fan.living_room_fan\n", "- vacuum => vacuum.demo_vacuum_0_ground_floor\n", "- front door => lock.front_door\n", "- kitchen door => lock.kitchen_door\n", "- kitchen window => cover.kitchen_window\n", "- living room window => cover.living_room_window\n", "- siren => siren.siren\n", "- 卧室灯 => light.bed_light\n", "- 天花板灯 => light.ceiling_lights\n", "- 厨房灯 => light.kitchen_lights\n", "- 客厅风扇 => fan.living_room_fan\n", "- 吊扇 => fan.ceiling_fan\n", "- 厨房窗户 => cover.kitchen_window\n", "- 客厅窗户 => cover.living_room_window\n", "- 前门 => lock.front_door\n", "- 厨房门 => lock.kitchen_door\n", "- 扫地机 => vacuum.demo_vacuum_0_ground_floor\n", "- 加湿器 => humidifier.humidifier\n", "- 警报器 => siren.siren\n"]}], "source": ["BY_NAME_CN = {\n", "    \"卧室灯\": [\"light.bed_light\"],\n", "    \"天花板灯\": [\"light.ceiling_lights\"],\n", "    \"厨房灯\": [\"light.kitchen_lights\"],\n", "    \"客厅风扇\": [\"fan.living_room_fan\"],\n", "    \"吊扇\": [\"fan.ceiling_fan\"],\n", "    \"厨房窗户\": [\"cover.kitchen_window\"],\n", "    \"客厅窗户\": [\"cover.living_room_window\"],\n", "    \"前门\": [\"lock.front_door\"],\n", "    \"厨房门\": [\"lock.kitchen_door\"],\n", "    \"扫地机\": [\"vacuum.demo_vacuum_0_ground_floor\"],\n", "    \"加湿器\": [\"humidifier.humidifier\"],\n", "    \"警报器\": [\"siren.siren\"],\n", "}\n", "HA_INDEX[\"by_name\"].update(BY_NAME_CN)\n", "\n", "def build_entity_hints(ha_index: dict) -> str:\n", "    lines = []\n", "    for alias, eids in ha_index.get(\"by_name\", {}).items():\n", "        eids_s = \", \".join(eids)\n", "        lines.append(f\"- {alias} => {eids_s}\")\n", "    return \"\\n\".join(lines)\n", "\n", "print(build_entity_hints(HA_INDEX))\n"]}, {"cell_type": "code", "execution_count": 5, "id": "1e4cf738", "metadata": {}, "outputs": [], "source": ["DOMAIN_MAP = {\"lights\": \"light\", \"fans\": \"fan\"}\n", "SERVICE_MAP = {\n", "  \"light\": {\"set_brightness\": \"turn_on\", \"turn_on\": \"turn_on\", \"turn_off\": \"turn_off\"},\n", "  \"fan\": {\"set_speed\": \"set_percentage\", \"set_percentage\": \"set_percentage\", \"turn_on\": \"turn_on\", \"turn_off\": \"turn_off\"},\n", "  \"cover\": {\"open\": \"open_cover\", \"close\": \"close_cover\", \"stop\": \"stop_cover\"},\n", "  \"lock\": {\"lock\": \"lock\", \"unlock\": \"unlock\"},\n", "  \"humidifier\": {\"set_humidity\": \"set_humidity\", \"turn_on\": \"turn_on\", \"turn_off\": \"turn_off\"},\n", "  \"vacuum\": {\"start\": \"start\", \"pause\": \"pause\", \"stop\": \"stop\", \"return\": \"return_to_base\", \"set_fan_speed\": \"set_fan_speed\"},\n", "  \"siren\": {\"turn_on\": \"turn_on\", \"turn_off\": \"turn_off\"}\n", "}\n", "\n", "def resolve_entity_id(text: str) -> str | None:\n", "    print(f\"🔍 resolve_entity_id: text={text}\")\n", "    text_norm = text.strip().lower()\n", "    print(f\"🔍 resolve_entity_id: text={text}\")\n", "\n", "    if \".\" in text_norm and text_norm in HA_INDEX[\"by_entity\"]:\n", "        print(f\"🔍 found in by_entity: {text_norm}\")\n", "        return text_norm\n", "    if text_norm in HA_INDEX[\"by_name\"]:\n", "        result = HA_INDEX[\"by_name\"][text_norm][0]\n", "        print(f\"🔍 found in by_name: {text_norm} -> {result}\")\n", "        return HA_INDEX[\"by_name\"][text_norm][0]\n", "    for eid, meta in HA_INDEX[\"by_entity\"].items():\n", "        if text_norm in meta.get(\"friendly_name\",\"\").lower():\n", "            print(f\"🔍 found in friendly_name: {text_norm} -> {eid}\")\n", "            return eid\n", "    return None\n", "\n", "def normalize_cmd(cmd: dict) -> dict:\n", "    eid = resolve_entity_id(cmd[\"entity_id\"])\n", "    if not eid:\n", "        raise SystemExit(f\"实体无法解析: {cmd['entity_id']}\")\n", "    domain = eid.split(\".\",1)[0]\n", "    service_std = SERVICE_MAP.get(domain, {}).get(cmd[\"service\"], cmd[\"service\"])\n", "    if service_std not in HA_INDEX[\"by_entity\"][eid][\"services\"]:\n", "        raise SystemExit(f\"服务不被允许: {domain}.{service_std}\")\n", "    args = cmd.get(\"args\", {})\n", "    return {\"domain\": domain, \"service\": service_std, \"entity_id\": eid, \"args\": args}\n", "\n", "def run_cmd_safe(cmd: dict, check_state=True):\n", "    fixed = normalize_cmd(cmd)\n", "    print(\"=== 指令 ===\")\n", "    jprint(fixed)\n", "    resp = call_service(fixed[\"domain\"], fixed[\"service\"], fixed[\"entity_id\"], **fixed[\"args\"])\n", "    print(\"=== 返回 ===\")\n", "    jprint(resp)\n", "    if check_state:\n", "        st = get_state(fixed[\"entity_id\"])\n", "        print(\"=== 最新状态 ===\")\n", "        jprint(st)\n", "    return fixed, resp\n"]}, {"cell_type": "code", "execution_count": 6, "id": "01261021", "metadata": {}, "outputs": [], "source": ["# === 覆盖并增强：域名/服务别名映射 ===\n", "\n", "# 允许的真实域（用于 grammar 不收敛时的强约束参考）\n", "ALLOWED_DOMAINS = {\"light\",\"fan\",\"cover\",\"lock\",\"humidifier\",\"vacuum\",\"siren\",\"sensor\"}\n", "\n", "# 常见“口误域名”→ 真实域；空串表示“忽略它，用 entity_id 前缀判定”\n", "DOMAIN_MAP.update({\n", "    \"homeassistant\": \"\",\n", "    \"home\": \"\",\n", "    \"humidity\": \"humidifier\",     # 把“humidity”口误归到 humidifier\n", "    \"lights\": \"light\",\n", "    \"fans\": \"fan\",\n", "    \"sirens\": \"siren\",\n", "    \"covers\": \"cover\",\n", "    \"locks\": \"lock\",\n", "    \"vacuums\": \"vacuum\",\n", "})\n", "\n", "# 各域服务别名更激进地收敛到标准服务\n", "SERVICE_MAP.setdefault(\"light\", {}).update({\n", "    \"set_light\": \"turn_on\",\n", "    \"set\": \"turn_on\",\n", "})\n", "SERVICE_MAP.setdefault(\"fan\", {}).update({\n", "    \"set\": \"set_percentage\",\n", "})\n", "SERVICE_MAP.setdefault(\"cover\", {}).update({\n", "    \"open\": \"open_cover\",\n", "    \"close\": \"close_cover\",\n", "    \"stop\": \"stop_cover\",\n", "})\n", "SERVICE_MAP.setdefault(\"lock\", {}).update({\n", "    # 有时模型把实体拼进 service，比如 \"lock.front_door\"\n", "    \"lock.front_door\": \"lock\",\n", "    \"unlock.front_door\": \"unlock\",\n", "})\n", "SERVICE_MAP.setdefault(\"humidifier\", {}).update({\n", "    \"set\": \"set_humidity\",\n", "})\n", "SERVICE_MAP.setdefault(\"vacuum\", {}).update({\n", "    \"dock\": \"return_to_base\",\n", "    \"return\": \"return_to_base\",\n", "})\n", "\n", "# 将“通用 value”参数映射到各域的真实字段\n", "ARG_KEY_MAP = {\n", "    \"light.turn_on\": {\"value\": \"brightness_pct\", \"percentage\": \"brightness_pct\"},\n", "    \"fan.set_percentage\": {\"value\": \"percentage\"},\n", "    \"humidifier.set_humidity\": {\"value\": \"humidity\", \"humidity_pct\": \"humidity\"},\n", "    \"vacuum.set_fan_speed\": {\"speed\": \"fan_speed\", \"value\": \"fan_speed\"},\n", "}\n", "\n", "def _remap_args(domain: str, service: str, args: dict) -> dict:\n", "    args = dict(args or {})\n", "    key_map = ARG_KEY_MAP.get(f\"{domain}.{service}\", {})\n", "    for k_src, k_dst in list(key_map.items()):\n", "        if k_src in args and k_dst not in args:\n", "            args[k_dst] = args.pop(k_src)\n", "    return args\n"]}, {"cell_type": "code", "execution_count": 7, "id": "885d53a1", "metadata": {}, "outputs": [], "source": ["# === 增强：基于用户原句的“实体优先级”纠错（当 LLM 挑错实体时兜底）===\n", "\n", "# 关键字 → 建议实体 的启发式（根据你的 demo 面板）\n", "INTENT_ALIAS_HINTS = [\n", "    ((\"卧室灯\",\"bed\",\"卧室\"), \"light.bed_light\"),\n", "    ((\"天花板灯\",\"顶灯\",\"ceiling\"), \"light.ceiling_lights\"),\n", "    ((\"厨房灯\",\"厨房\"), \"light.kitchen_lights\"),\n", "    ((\"客厅风扇\",\"客厅风\"), \"fan.living_room_fan\"),\n", "    ((\"吊扇\"), \"fan.ceiling_fan\"),\n", "    ((\"厨房窗\",\"厨房窗户\",\"kitchen window\"), \"cover.kitchen_window\"),\n", "    ((\"客厅窗\",\"客厅窗户\",\"living room window\"), \"cover.living_room_window\"),\n", "    ((\"前门\",\"front door\"), \"lock.front_door\"),\n", "    ((\"厨房门\",\"kitchen door\"), \"lock.kitchen_door\"),\n", "    ((\"加湿器\",\"humidifier\"), \"humidifier.humidifier\"),\n", "    ((\"扫地机\",\"vacuum\"), \"vacuum.demo_vacuum_0_ground_floor\"),\n", "    ((\"警报器\",\"siren\",\"报警\"), \"siren.siren\"),\n", "]\n", "\n", "# ✅ 把这些追加到你已有的 INTENT_ALIAS_HINTS 之前（提高优先级）\n", "EXTRA_HINTS = [\n", "    ((\"厨房窗\", \"厨房的窗\", \"厨房窗户\", \"开厨房窗\", \"打开厨房窗\", \"打开厨房的窗\", \"打开厨房窗户\", \"kitchen window\"),\n", "     \"cover.kitchen_window\"),\n", "    ((\"客厅窗\", \"客厅的窗\", \"客厅窗户\", \"开客厅窗\", \"打开客厅窗\", \"打开客厅的窗\", \"打开客厅窗户\", \"living room window\"),\n", "     \"cover.living_room_window\"),\n", "]\n", "\n", "# 如果你之前已定义 INTENT_ALIAS_HINTS，这里做“前置合并”，避免重复\n", "try:\n", "    # 去重合并（保持 EXTRA_HINTS 在最前）\n", "    _existing = set((tuple(k if isinstance(k, tuple) else (k,)), v) for k, v in INTENT_ALIAS_HINTS)\n", "    _new = []\n", "    for k, v in EXTRA_HINTS:\n", "        key = (tuple(k if isinstance(k, tuple) else (k,)), v)\n", "        if key not in _existing:\n", "            _new.append((k, v))\n", "    INTENT_ALIAS_HINTS = _new + INTENT_ALIAS_HINTS\n", "except NameError:\n", "    # 还没定义 INTENT_ALIAS_HINTS 的情况\n", "    INTENT_ALIAS_HINTS = list(EXTRA_HINTS)\n", "\n", "\n", "\n", "\n", "def resolve_entity_id_smart(entity_text: str, user_text: str | None = None) -> str | None:\n", "    \"\"\"\n", "    先用你原来的 resolve_entity_id；若失败或领域/动作冲突，再用 user_text 里的关键词兜底。\n", "    \"\"\"\n", "    print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "    eid = resolve_entity_id(entity_text) if entity_text else None\n", "    print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "\n", "    if eid:\n", "        return eid\n", "    ut = (user_text or \"\").lower()\n", "\n", "    print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "    \n", "    for keywords, candidate in INTENT_ALIAS_HINTS:\n", "        if isinstance(keywords, str):\n", "            keywords = (keywords,)\n", "        if any(k.lower() in ut for k in keywords):\n", "            print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "            return candidate\n", "        \n", "    print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "    return None\n", "\n", "def normalize_cmd_with_text(cmd: dict, user_text: str | None = None) -> dict:\n", "    \"\"\"\n", "    比 normalize_cmd 更强：\n", "      1) entity_id 优先从 LLM 输出解析，若不行用 user_text 关键词兜底；\n", "      2) domain 以 entity_id 前缀为准，若 LLM 给了“homeassistant”等则忽略；\n", "      3) service 用 SERVICE_MAP 收敛；若 service 不合法，结合域的典型操作做一次强纠；\n", "      4) 参数把 value/percentage/humidity_pct 等映射成各自真实字段，并做范围截断。\n", "    \"\"\"\n", "    raw_domain  = (cmd.get(\"domain\")  or \"\").strip().lower()\n", "    raw_service = (cmd.get(\"service\") or \"\").strip().lower()\n", "    raw_eid     = (cmd.get(\"entity_id\") or \"\").strip()\n", "    raw_args    = cmd.get(\"args\") or {}\n", "\n", "    print(f\"🔍 DEBUG: raw_domain={raw_domain}, raw_service={raw_service}, raw_eid={raw_eid}\")\n", "\n", "    # 1) 实体解析（允许别名/友好名/关键词兜底）\n", "    eid = resolve_entity_id_smart(raw_eid, user_text=user_text)\n", "    if not eid:\n", "        raise SystemExit(f\"实体无法解析: {raw_eid!r}\")\n", "\n", "    true_domain = eid.split(\".\", 1)[0]\n", "\n", "    # 2) 规范域：优先 entity_id 前缀，其次 DOMAIN_MAP\n", "    mapped = DOMAIN_MAP.get(raw_domain, raw_domain)\n", "    domain = true_domain if (mapped in (\"\", None) or mapped not in ALLOWED_DOMAINS) else mapped\n", "\n", "    # 3) 规范服务：基于最终确定的 domain 重新映射 service\n", "    service = SERVICE_MAP.get(domain, {}).get(raw_service, raw_service)\n", "\n", "    # 4) 先把参数做一次键名映射（value→brightness_pct 等）\n", "    args = _remap_args(domain, service, raw_args)\n", "\n", "    # 5) 白名单校验（服务允许性 & 允许参数）\n", "    meta = HA_INDEX[\"by_entity\"].get(eid)\n", "    if not meta:\n", "        raise SystemExit(f\"实体不在白名单: {eid}\")\n", "    allowed_svcs = set(meta.get(\"services\", []))\n", "    if service not in allowed_svcs:\n", "        # 按域做一次“强纠”：根据用户意图关键字修成最常见服务\n", "        # 灯：默认 turn_on；风扇：set_percentage（若出现数字/百分比）；窗帘：open/close；锁：lock/unlock；加湿器：set_humidity；扫地机：start\n", "        ut = (user_text or \"\").lower()\n", "        if domain == \"light\":\n", "            service = \"turn_on\"\n", "        elif domain == \"fan\":\n", "            service = \"set_percentage\" if any(x in (args.keys() | {\"value\",\"percentage\"}) for x in (\"percentage\",\"value\")) or re.search(r\"\\d+%?\", ut) else \"turn_on\"\n", "        elif domain == \"cover\":\n", "            service = \"open_cover\" if any(k in ut for k in (\"开\",\"open\")) else \"close_cover\" if any(k in ut for k in (\"关\",\"close\")) else \"stop_cover\"\n", "        elif domain == \"lock\":\n", "            service = \"lock\" if any(k in ut for k in (\"锁上\",\"上锁\",\"lock\")) else \"unlock\" if \"解锁\" in ut or \"unlock\" in ut else \"lock\"\n", "        elif domain == \"humidifier\":\n", "            service = \"set_humidity\" if any(k in ut for k in (\"%\",\"湿\")) or any(k in args for k in (\"humidity\",\"value\",\"humidity_pct\")) else \"turn_on\"\n", "        elif domain == \"vacuum\":\n", "            service = \"start\"\n", "        elif domain == \"siren\":\n", "            service = \"turn_on\"\n", "        # 再次校验\n", "        if service not in allowed_svcs:\n", "            raise SystemExit(f\"服务不被允许: {domain}.{service} for {eid}; 允许: {sorted(allowed_svcs)}\")\n", "\n", "    # 6) 只保留允许参数，并做数值范围归一\n", "    allowed_args = set(meta.get(\"args\", {}).get(service, []))\n", "    args = {k:v for k,v in args.items() if k in allowed_args}\n", "\n", "    # 数值归一\n", "    if domain == \"light\" and service == \"turn_on\" and \"brightness_pct\" in args:\n", "        try:\n", "            args[\"brightness_pct\"] = max(0, min(100, int(args[\"brightness_pct\"])))\n", "        except Exception:\n", "            args.pop(\"brightness_pct\", None)\n", "\n", "    if domain == \"fan\" and service == \"set_percentage\" and \"percentage\" in args:\n", "        try:\n", "            args[\"percentage\"] = max(0, min(100, int(args[\"percentage\"])))\n", "        except Exception:\n", "            raise SystemExit(\"fan.set_percentage 需要整数 percentage 0-100\")\n", "\n", "    if domain == \"humidifier\" and service == \"set_humidity\" and \"humidity\" in args:\n", "        try:\n", "            args[\"humidity\"] = max(0, min(100, int(args[\"humidity\"])))\n", "        except Exception:\n", "            raise SystemExit(\"humidifier.set_humidity 需要整数 humidity 0-100\")\n", "\n", "    return {\"domain\": domain, \"service\": service, \"entity_id\": eid, \"args\": args}\n", "\n", "    # 在函数最后，return 之前添加：\n", "    print(f\"🔍 FINAL: domain={domain}, service={service}, entity_id={eid}, args={args}\")\n", "    return {\"domain\": domain, \"service\": service, \"entity_id\": eid, \"args\": args}"]}, {"cell_type": "code", "execution_count": 8, "id": "1676b399", "metadata": {}, "outputs": [{"data": {"text/plain": ["[(('厨房窗',\n", "   '厨房的窗',\n", "   '厨房窗户',\n", "   '开厨房窗',\n", "   '打开厨房窗',\n", "   '打开厨房的窗',\n", "   '打开厨房窗户',\n", "   'kitchen window'),\n", "  'cover.kitchen_window'),\n", " (('客厅窗',\n", "   '客厅的窗',\n", "   '客厅窗户',\n", "   '开客厅窗',\n", "   '打开客厅窗',\n", "   '打开客厅的窗',\n", "   '打开客厅窗户',\n", "   'living room window'),\n", "  'cover.living_room_window'),\n", " (('卧室灯', 'bed', '卧室'), 'light.bed_light'),\n", " (('天花板灯', '顶灯', 'ceiling'), 'light.ceiling_lights'),\n", " (('厨房灯', '厨房'), 'light.kitchen_lights'),\n", " (('客厅风扇', '客厅风'), 'fan.living_room_fan'),\n", " ('吊扇', 'fan.ceiling_fan'),\n", " (('厨房窗', '厨房窗户', 'kitchen window'), 'cover.kitchen_window'),\n", " (('客厅窗', '客厅窗户', 'living room window'), 'cover.living_room_window'),\n", " (('前门', 'front door'), 'lock.front_door'),\n", " (('厨房门', 'kitchen door'), 'lock.kitchen_door'),\n", " (('加湿器', 'humidifier'), 'humidifier.humidifier'),\n", " (('扫地机', 'vacuum'), 'vacuum.demo_vacuum_0_ground_floor'),\n", " (('警报器', 'siren', '报警'), 'siren.siren')]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["INTENT_ALIAS_HINTS"]}, {"cell_type": "code", "execution_count": 9, "id": "2f68080e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["llama_model_loader: loaded meta data with 26 key-value pairs and 291 tensors from /home/<USER>/LLM_Models/Qwen2.5-0.5B-Instruct-GGUF/qwen2.5-0.5b-instruct-fp16.gguf (version GGUF V3 (latest))\n", "llama_model_loader: Dumping metadata keys/values. Note: KV overrides do not apply in this output.\n", "llama_model_loader: - kv   0:                       general.architecture str              = qwen2\n", "llama_model_loader: - kv   1:                               general.type str              = model\n", "llama_model_loader: - kv   2:                               general.name str              = qwen2.5-0.5b-instruct\n", "llama_model_loader: - kv   3:                            general.version str              = v0.1\n", "llama_model_loader: - kv   4:                           general.finetune str              = qwen2.5-0.5b-instruct\n", "llama_model_loader: - kv   5:                         general.size_label str              = 630M\n", "llama_model_loader: - kv   6:                          qwen2.block_count u32              = 24\n", "llama_model_loader: - kv   7:                       qwen2.context_length u32              = 8192\n", "llama_model_loader: - kv   8:                     qwen2.embedding_length u32              = 896\n", "llama_model_loader: - kv   9:                  qwen2.feed_forward_length u32              = 4864\n", "llama_model_loader: - kv  10:                 qwen2.attention.head_count u32              = 14\n", "llama_model_loader: - kv  11:              qwen2.attention.head_count_kv u32              = 2\n", "llama_model_loader: - kv  12:                       qwen2.rope.freq_base f32              = 1000000.000000\n", "llama_model_loader: - kv  13:     qwen2.attention.layer_norm_rms_epsilon f32              = 0.000001\n", "llama_model_loader: - kv  14:                          general.file_type u32              = 1\n", "llama_model_loader: - kv  15:                       tokenizer.ggml.model str              = gpt2\n", "llama_model_loader: - kv  16:                         tokenizer.ggml.pre str              = qwen2\n", "llama_model_loader: - kv  17:                      tokenizer.ggml.tokens arr[str,151936]  = [\"!\", \"\\\"\", \"#\", \"$\", \"%\", \"&\", \"'\", ...\n", "llama_model_loader: - kv  18:                  tokenizer.ggml.token_type arr[i32,151936]  = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...\n", "llama_model_loader: - kv  19:                      tokenizer.ggml.merges arr[str,151387]  = [\"Ġ Ġ\", \"ĠĠ ĠĠ\", \"i n\", \"Ġ t\",...\n", "llama_model_loader: - kv  20:                tokenizer.ggml.eos_token_id u32              = 151645\n", "llama_model_loader: - kv  21:            tokenizer.ggml.padding_token_id u32              = 151643\n", "llama_model_loader: - kv  22:                tokenizer.ggml.bos_token_id u32              = 151643\n", "llama_model_loader: - kv  23:               tokenizer.ggml.add_bos_token bool             = false\n", "llama_model_loader: - kv  24:                    tokenizer.chat_template str              = {%- if tools %}\\n    {{- '<|im_start|>...\n", "llama_model_loader: - kv  25:               general.quantization_version u32              = 2\n", "llama_model_loader: - type  f32:  121 tensors\n", "llama_model_loader: - type  f16:  170 tensors\n", "print_info: file format = GGUF V3 (latest)\n", "print_info: file type   = F16\n", "print_info: file size   = 1.17 GiB (16.00 BPW) \n", "init_tokenizer: initializing tokenizer for type 2\n", "load: control token: 151660 '<|fim_middle|>' is not marked as EOG\n", "load: control token: 151659 '<|fim_prefix|>' is not marked as EOG\n", "load: control token: 151653 '<|vision_end|>' is not marked as EOG\n", "load: control token: 151648 '<|box_start|>' is not marked as EOG\n", "load: control token: 151646 '<|object_ref_start|>' is not marked as EOG\n", "load: control token: 151649 '<|box_end|>' is not marked as EOG\n", "load: control token: 151655 '<|image_pad|>' is not marked as EOG\n", "load: control token: 151651 '<|quad_end|>' is not marked as EOG\n", "load: control token: 151647 '<|object_ref_end|>' is not marked as EOG\n", "load: control token: 151652 '<|vision_start|>' is not marked as EOG\n", "load: control token: 151654 '<|vision_pad|>' is not marked as EOG\n", "load: control token: 151656 '<|video_pad|>' is not marked as EOG\n", "load: control token: 151644 '<|im_start|>' is not marked as EOG\n", "load: control token: 151661 '<|fim_suffix|>' is not marked as EOG\n", "load: control token: 151650 '<|quad_start|>' is not marked as EOG\n", "load: printing all EOG tokens:\n", "load:   - 151643 ('<|endoftext|>')\n", "load:   - 151645 ('<|im_end|>')\n", "load:   - 151662 ('<|fim_pad|>')\n", "load:   - 151663 ('<|repo_name|>')\n", "load:   - 151664 ('<|file_sep|>')\n", "load: special tokens cache size = 22\n", "load: token to piece cache size = 0.9310 MB\n", "print_info: arch             = qwen2\n", "print_info: vocab_only       = 0\n", "print_info: n_ctx_train      = 8192\n", "print_info: n_embd           = 896\n", "print_info: n_layer          = 24\n", "print_info: n_head           = 14\n", "print_info: n_head_kv        = 2\n", "print_info: n_rot            = 64\n", "print_info: n_swa            = 0\n", "print_info: is_swa_any       = 0\n", "print_info: n_embd_head_k    = 64\n", "print_info: n_embd_head_v    = 64\n", "print_info: n_gqa            = 7\n", "print_info: n_embd_k_gqa     = 128\n", "print_info: n_embd_v_gqa     = 128\n", "print_info: f_norm_eps       = 0.0e+00\n", "print_info: f_norm_rms_eps   = 1.0e-06\n", "print_info: f_clamp_kqv      = 0.0e+00\n", "print_info: f_max_alibi_bias = 0.0e+00\n", "print_info: f_logit_scale    = 0.0e+00\n", "print_info: f_attn_scale     = 0.0e+00\n", "print_info: n_ff             = 4864\n", "print_info: n_expert         = 0\n", "print_info: n_expert_used    = 0\n", "print_info: causal attn      = 1\n", "print_info: pooling type     = -1\n", "print_info: rope type        = 2\n", "print_info: rope scaling     = linear\n", "print_info: freq_base_train  = 1000000.0\n", "print_info: freq_scale_train = 1\n", "print_info: n_ctx_orig_yarn  = 8192\n", "print_info: rope_finetuned   = unknown\n", "print_info: model type       = 1B\n", "print_info: model params     = 630.17 M\n", "print_info: general.name     = qwen2.5-0.5b-instruct\n", "print_info: vocab type       = BPE\n", "print_info: n_vocab          = 151936\n", "print_info: n_merges         = 151387\n", "print_info: BOS token        = 151643 '<|endoftext|>'\n", "print_info: EOS token        = 151645 '<|im_end|>'\n", "print_info: EOT token        = 151645 '<|im_end|>'\n", "print_info: PAD token        = 151643 '<|endoftext|>'\n", "print_info: LF token         = 198 'Ċ'\n", "print_info: FIM PRE token    = 151659 '<|fim_prefix|>'\n", "print_info: FIM SUF token    = 151661 '<|fim_suffix|>'\n", "print_info: FIM MID token    = 151660 '<|fim_middle|>'\n", "print_info: FIM PAD token    = 151662 '<|fim_pad|>'\n", "print_info: FIM REP token    = 151663 '<|repo_name|>'\n", "print_info: FIM SEP token    = 151664 '<|file_sep|>'\n", "print_info: EOG token        = 151643 '<|endoftext|>'\n", "print_info: EOG token        = 151645 '<|im_end|>'\n", "print_info: EOG token        = 151662 '<|fim_pad|>'\n", "print_info: EOG token        = 151663 '<|repo_name|>'\n", "print_info: EOG token        = 151664 '<|file_sep|>'\n", "print_info: max token length = 256\n", "load_tensors: loading model tensors, this can take a while... (mmap = true)\n", "load_tensors: layer   0 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   1 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   2 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   3 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   4 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   5 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   6 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   7 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   8 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   9 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  10 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  11 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  12 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  13 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  14 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  15 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  16 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  17 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  18 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  19 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  20 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  21 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  22 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  23 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  24 assigned to device CPU, is_swa = 0\n", "load_tensors: tensor 'token_embd.weight' (f16) (and 290 others) cannot be used with preferred buffer type CPU_REPACK, using CPU instead\n", "load_tensors:   CPU_Mapped model buffer size =  1202.09 MiB\n", "...........................................................\n", "llama_context: constructing llama_context\n", "llama_context: n_seq_max     = 1\n", "llama_context: n_ctx         = 2048\n", "llama_context: n_ctx_per_seq = 2048\n", "llama_context: n_batch       = 512\n", "llama_context: n_ubatch      = 512\n", "llama_context: causal_attn   = 1\n", "llama_context: flash_attn    = 0\n", "llama_context: kv_unified    = false\n", "llama_context: freq_base     = 1000000.0\n", "llama_context: freq_scale    = 1\n", "llama_context: n_ctx_per_seq (2048) < n_ctx_train (8192) -- the full capacity of the model will not be utilized\n", "set_abort_callback: call\n", "llama_context:        CPU  output buffer size =     0.58 MiB\n", "create_memory: n_ctx = 2048 (padded)\n", "llama_kv_cache_unified: layer   0: dev = CPU\n", "llama_kv_cache_unified: layer   1: dev = CPU\n", "llama_kv_cache_unified: layer   2: dev = CPU\n", "llama_kv_cache_unified: layer   3: dev = CPU\n", "llama_kv_cache_unified: layer   4: dev = CPU\n", "llama_kv_cache_unified: layer   5: dev = CPU\n", "llama_kv_cache_unified: layer   6: dev = CPU\n", "llama_kv_cache_unified: layer   7: dev = CPU\n", "llama_kv_cache_unified: layer   8: dev = CPU\n", "llama_kv_cache_unified: layer   9: dev = CPU\n", "llama_kv_cache_unified: layer  10: dev = CPU\n", "llama_kv_cache_unified: layer  11: dev = CPU\n", "llama_kv_cache_unified: layer  12: dev = CPU\n", "llama_kv_cache_unified: layer  13: dev = CPU\n", "llama_kv_cache_unified: layer  14: dev = CPU\n", "llama_kv_cache_unified: layer  15: dev = CPU\n", "llama_kv_cache_unified: layer  16: dev = CPU\n", "llama_kv_cache_unified: layer  17: dev = CPU\n", "llama_kv_cache_unified: layer  18: dev = CPU\n", "llama_kv_cache_unified: layer  19: dev = CPU\n", "llama_kv_cache_unified: layer  20: dev = CPU\n", "llama_kv_cache_unified: layer  21: dev = CPU\n", "llama_kv_cache_unified: layer  22: dev = CPU\n", "llama_kv_cache_unified: layer  23: dev = CPU\n", "llama_kv_cache_unified:        CPU KV buffer size =    24.00 MiB\n", "llama_kv_cache_unified: size =   24.00 MiB (  2048 cells,  24 layers,  1/1 seqs), K (f16):   12.00 MiB, V (f16):   12.00 MiB\n", "llama_context: enumerating backends\n", "llama_context: backend_ptrs.size() = 2\n", "llama_context: max_nodes = 2328\n", "llama_context: worst-case: n_tokens = 512, n_seqs = 1, n_outputs = 0\n", "graph_reserve: reserving a graph for ubatch with n_tokens =  512, n_seqs =  1, n_outputs =  512\n", "graph_reserve: reserving a graph for ubatch with n_tokens =    1, n_seqs =  1, n_outputs =    1\n", "graph_reserve: reserving a graph for ubatch with n_tokens =  512, n_seqs =  1, n_outputs =  512\n", "llama_context:        CPU compute buffer size =   298.50 MiB\n", "llama_context: graph nodes  = 918\n", "llama_context: graph splits = 338 (with bs=512), 1 (with bs=1)\n", "CPU : SSE3 = 1 | LLAMAFILE = 1 | OPENMP = 1 | REPACK = 1 | \n", "Model metadata: {'tokenizer.ggml.add_bos_token': 'false', 'tokenizer.ggml.bos_token_id': '151643', 'general.file_type': '1', 'qwen2.attention.layer_norm_rms_epsilon': '0.000001', 'general.architecture': 'qwen2', 'tokenizer.ggml.padding_token_id': '151643', 'qwen2.embedding_length': '896', 'tokenizer.ggml.pre': 'qwen2', 'general.name': 'qwen2.5-0.5b-instruct', 'qwen2.block_count': '24', 'general.version': 'v0.1', 'tokenizer.ggml.eos_token_id': '151645', 'qwen2.rope.freq_base': '1000000.000000', 'general.finetune': 'qwen2.5-0.5b-instruct', 'general.type': 'model', 'general.size_label': '630M', 'qwen2.context_length': '8192', 'tokenizer.chat_template': '{%- if tools %}\\n    {{- \\'<|im_start|>system\\\\n\\' }}\\n    {%- if messages[0][\\'role\\'] == \\'system\\' %}\\n        {{- messages[0][\\'content\\'] }}\\n    {%- else %}\\n        {{- \\'You are Qwen, created by Alibaba Cloud. You are a helpful assistant.\\' }}\\n    {%- endif %}\\n    {{- \"\\\\n\\\\n# Tools\\\\n\\\\nYou may call one or more functions to assist with the user query.\\\\n\\\\nYou are provided with function signatures within <tools></tools> XML tags:\\\\n<tools>\" }}\\n    {%- for tool in tools %}\\n        {{- \"\\\\n\" }}\\n        {{- tool | tojson }}\\n    {%- endfor %}\\n    {{- \"\\\\n</tools>\\\\n\\\\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\\\\n<tool_call>\\\\n{{\\\\\"name\\\\\": <function-name>, \\\\\"arguments\\\\\": <args-json-object>}}\\\\n</tool_call><|im_end|>\\\\n\" }}\\n{%- else %}\\n    {%- if messages[0][\\'role\\'] == \\'system\\' %}\\n        {{- \\'<|im_start|>system\\\\n\\' + messages[0][\\'content\\'] + \\'<|im_end|>\\\\n\\' }}\\n    {%- else %}\\n        {{- \\'<|im_start|>system\\\\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.<|im_end|>\\\\n\\' }}\\n    {%- endif %}\\n{%- endif %}\\n{%- for message in messages %}\\n    {%- if (message.role == \"user\") or (message.role == \"system\" and not loop.first) or (message.role == \"assistant\" and not message.tool_calls) %}\\n        {{- \\'<|im_start|>\\' + message.role + \\'\\\\n\\' + message.content + \\'<|im_end|>\\' + \\'\\\\n\\' }}\\n    {%- elif message.role == \"assistant\" %}\\n        {{- \\'<|im_start|>\\' + message.role }}\\n        {%- if message.content %}\\n            {{- \\'\\\\n\\' + message.content }}\\n        {%- endif %}\\n        {%- for tool_call in message.tool_calls %}\\n            {%- if tool_call.function is defined %}\\n                {%- set tool_call = tool_call.function %}\\n            {%- endif %}\\n            {{- \\'\\\\n<tool_call>\\\\n{\"name\": \"\\' }}\\n            {{- tool_call.name }}\\n            {{- \\'\", \"arguments\": \\' }}\\n            {{- tool_call.arguments | tojson }}\\n            {{- \\'}\\\\n</tool_call>\\' }}\\n        {%- endfor %}\\n        {{- \\'<|im_end|>\\\\n\\' }}\\n    {%- elif message.role == \"tool\" %}\\n        {%- if (loop.index0 == 0) or (messages[loop.index0 - 1].role != \"tool\") %}\\n            {{- \\'<|im_start|>user\\' }}\\n        {%- endif %}\\n        {{- \\'\\\\n<tool_response>\\\\n\\' }}\\n        {{- message.content }}\\n        {{- \\'\\\\n</tool_response>\\' }}\\n        {%- if loop.last or (messages[loop.index0 + 1].role != \"tool\") %}\\n            {{- \\'<|im_end|>\\\\n\\' }}\\n        {%- endif %}\\n    {%- endif %}\\n{%- endfor %}\\n{%- if add_generation_prompt %}\\n    {{- \\'<|im_start|>assistant\\\\n\\' }}\\n{%- endif %}\\n', 'qwen2.attention.head_count_kv': '2', 'general.quantization_version': '2', 'tokenizer.ggml.model': 'gpt2', 'qwen2.feed_forward_length': '4864', 'qwen2.attention.head_count': '14'}\n", "Available chat formats from metadata: chat_template.default\n", "Using gguf chat template: {%- if tools %}\n", "    {{- '<|im_start|>system\\n' }}\n", "    {%- if messages[0]['role'] == 'system' %}\n", "        {{- messages[0]['content'] }}\n", "    {%- else %}\n", "        {{- 'You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.' }}\n", "    {%- endif %}\n", "    {{- \"\\n\\n# Tools\\n\\nYou may call one or more functions to assist with the user query.\\n\\nYou are provided with function signatures within <tools></tools> XML tags:\\n<tools>\" }}\n", "    {%- for tool in tools %}\n", "        {{- \"\\n\" }}\n", "        {{- tool | tojson }}\n", "    {%- endfor %}\n", "    {{- \"\\n</tools>\\n\\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\\n<tool_call>\\n{{\\\"name\\\": <function-name>, \\\"arguments\\\": <args-json-object>}}\\n</tool_call><|im_end|>\\n\" }}\n", "{%- else %}\n", "    {%- if messages[0]['role'] == 'system' %}\n", "        {{- '<|im_start|>system\\n' + messages[0]['content'] + '<|im_end|>\\n' }}\n", "    {%- else %}\n", "        {{- '<|im_start|>system\\nYou are <PERSON><PERSON>, created by Alibaba Cloud. You are a helpful assistant.<|im_end|>\\n' }}\n", "    {%- endif %}\n", "{%- endif %}\n", "{%- for message in messages %}\n", "    {%- if (message.role == \"user\") or (message.role == \"system\" and not loop.first) or (message.role == \"assistant\" and not message.tool_calls) %}\n", "        {{- '<|im_start|>' + message.role + '\\n' + message.content + '<|im_end|>' + '\\n' }}\n", "    {%- elif message.role == \"assistant\" %}\n", "        {{- '<|im_start|>' + message.role }}\n", "        {%- if message.content %}\n", "            {{- '\\n' + message.content }}\n", "        {%- endif %}\n", "        {%- for tool_call in message.tool_calls %}\n", "            {%- if tool_call.function is defined %}\n", "                {%- set tool_call = tool_call.function %}\n", "            {%- endif %}\n", "            {{- '\\n<tool_call>\\n{\"name\": \"' }}\n", "            {{- tool_call.name }}\n", "            {{- '\", \"arguments\": ' }}\n", "            {{- tool_call.arguments | tojson }}\n", "            {{- '}\\n</tool_call>' }}\n", "        {%- endfor %}\n", "        {{- '<|im_end|>\\n' }}\n", "    {%- elif message.role == \"tool\" %}\n", "        {%- if (loop.index0 == 0) or (messages[loop.index0 - 1].role != \"tool\") %}\n", "            {{- '<|im_start|>user' }}\n", "        {%- endif %}\n", "        {{- '\\n<tool_response>\\n' }}\n", "        {{- message.content }}\n", "        {{- '\\n</tool_response>' }}\n", "        {%- if loop.last or (messages[loop.index0 + 1].role != \"tool\") %}\n", "            {{- '<|im_end|>\\n' }}\n", "        {%- endif %}\n", "    {%- endif %}\n", "{%- endfor %}\n", "{%- if add_generation_prompt %}\n", "    {{- '<|im_start|>assistant\\n' }}\n", "{%- endif %}\n", "\n", "Using chat eos_token: <|im_end|>\n", "Using chat bos_token: <|endoftext|>\n"]}], "source": ["MODEL_PATH = \"/home/<USER>/LLM_Models/Qwen2.5-0.5B-Instruct-GGUF/qwen2.5-0.5b-instruct-fp16.gguf\"\n", "llm = Llama(model_path=MODEL_PATH, n_threads=8, n_ctx=2048)\n", "\n", "GBNF_JSON = r'''\n", "ws ::= [ \\t\\n\\r]*\n", "root ::= ws object ws\n", "object ::= \"{\" ws pair (ws \",\" ws pair)* ws \"}\"\n", "pair ::= string ws \":\" ws value\n", "string ::= \"\\\"\" [^\\\"]* \"\\\"\"\n", "value ::= string | number | object | array | \"true\" | \"false\" | \"null\"\n", "array ::= \"[\" ws (value (ws \",\" ws value)*)? ws \"]\"\n", "number ::= \"-\"? [0-9]+ (\".\" [0-9]+)?\n", "'''\n", "grammar_json = LlamaGrammar.from_string(GBNF_JSON)\n", "\n", "def format_chat_qwen(messages):\n", "    out = []\n", "    for m in messages:\n", "        out.append(f\"<|im_start|>{m['role']}\\n{m['content']}<|im_end|>\\n\")\n", "    out.append(\"<|im_start|>assistant\\n\")\n", "    return \"\".join(out)\n", "\n", "SYSTEM_PROMPT = (\n", "    \"你是 Home Assistant 控制器，只能输出 JSON：\"\n", "    '{\"domain\":\"...\",\"service\":\"...\",\"entity_id\":\"...\",\"args\":{...}}。\\n'\n", "    \"实体提示：\\n\" + build_entity_hints(HA_INDEX)\n", ")\n", "\n", "def llm_parse_to_json(user_text: str):\n", "    prompt = format_chat_qwen([\n", "        {\"role\":\"system\",\"content\": SYSTEM_PROMPT},\n", "        {\"role\":\"user\",\"content\": user_text}\n", "    ])\n", "    out = llm.create_completion(prompt=prompt, max_tokens=128, temperature=0, grammar=grammar_json, stop=[\"<|im_end|>\"])\n", "    raw = out[\"choices\"][0][\"text\"].strip()\n", "    print(\"LLM RAW:\", raw)\n", "    return json.loads(raw)\n", "\n", "def execute_nl(user_text: str, check_state: bool = True):\n", "    cmd_raw = llm_parse_to_json(user_text)   # 仍旧用你现在的 LLM + 简单 JSON 语法\n", "    # ★ 关键：把原句一起传入，用于启发式兜底\n", "    fixed = normalize_cmd_with_text(cmd_raw, user_text=user_text)\n", "\n", "    print(\"=== 规范化指令 ===\")\n", "    jprint(fixed)\n", "\n", "    # 打印等价 curl\n", "    body = {\"entity_id\": fixed[\"entity_id\"]}; body.update(fixed.get(\"args\") or {})\n", "    curl = (\n", "        f\"curl -X POST '{HA_URL}/api/services/{fixed['domain']}/{fixed['service']}' \"\n", "        \"-H 'Content-Type: application/json' \"\n", "        \"-H 'Authorization: Bearer $HA_TOKEN' \"\n", "        f\"-d '{json.dumps(body, ensure_ascii=False)}'\"\n", "    )\n", "    print(\"\\n=== 等价 curl ===\")\n", "    print(curl)\n", "\n", "    # 真正执行\n", "    resp = call_service(fixed[\"domain\"], fixed[\"service\"], fixed[\"entity_id\"], **(fixed.get(\"args\") or {}))\n", "    try:\n", "        print(\"\\n=== 返回 ===\")\n", "        jprint(resp)\n", "    except Exception:\n", "        print(resp)\n", "\n", "    if check_state:\n", "        try:\n", "            st = get_state(fixed[\"entity_id\"])\n", "            print(\"\\n=== 最新状态 ===\")\n", "            jprint(st)\n", "        except Exception as e:\n", "            print(f\"[WARN] 读取状态失败：{e}\")\n", "\n", "    return fixed, resp\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "id": "46dd0aab", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["llama_perf_context_print:        load time =    2472.42 ms\n", "llama_perf_context_print: prompt eval time =    2472.04 ms /   316 tokens (    7.82 ms per token,   127.83 tokens per second)\n", "llama_perf_context_print:        eval time =    3370.34 ms /    41 runs   (   82.20 ms per token,    12.16 tokens per second)\n", "llama_perf_context_print:       total time =    6490.81 ms /   357 tokens\n", "llama_perf_context_print:    graphs reused =         38\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLM RAW: {\n", "  \"domain\": \"light\",\n", "  \"service\": \"open\",\n", "  \"entity_id\": \"light.kitchen_window\",\n", "  \"args\": {\n", "    \"direction\": \"out\"\n", "  }\n", "}\n", "🔍 DEBUG: raw_domain=light, raw_service=open, raw_eid=light.kitchen_window\n", "🔍 resolve_entity_id_smart: entity_text=light.kitchen_window, user_text=打开厨房的窗\n", "🔍 resolve_entity_id: text=light.kitchen_window\n", "🔍 resolve_entity_id: text=light.kitchen_window\n", "🔍 resolve_entity_id_smart: entity_text=light.kitchen_window, user_text=打开厨房的窗\n", "🔍 resolve_entity_id_smart: entity_text=light.kitchen_window, user_text=打开厨房的窗\n", "🔍 resolve_entity_id_smart: entity_text=light.kitchen_window, user_text=打开厨房的窗\n"]}, {"ename": "SystemExit", "evalue": "服务不被允许: light.turn_on for cover.kitchen_window; 允许: ['close_cover', 'open_cover', 'stop_cover']", "output_type": "error", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[0;31mSystemExit\u001b[0m\u001b[0;31m:\u001b[0m 服务不被允许: light.turn_on for cover.kitchen_window; 允许: ['close_cover', 'open_cover', 'stop_cover']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniforge3/envs/llamacpp/lib/python3.10/site-packages/IPython/core/interactiveshell.py:3587: UserWarning: To exit: use 'exit', 'quit', or Ctrl-D.\n", "  warn(\"To exit: use 'exit', 'quit', or Ctrl-D.\", stacklevel=1)\n"]}], "source": ["execute_nl(\"打开厨房的窗\")"]}, {"cell_type": "code", "execution_count": null, "id": "e56abefb", "metadata": {}, "outputs": [], "source": ["tests = [\n", "    \"把卧室灯开到 70%\",\n", "    \"把客厅风扇调到 50%\",\n", "    \"打开厨房窗户\",\n", "    \"锁上前门\",\n", "    \"加湿器设为 55%\",\n", "    \"扫地机调到 high 风速并开始\"\n", "]\n", "\n", "for t in tests:\n", "    print(\"\\n==============================\")\n", "    print(\"指令:\", t)\n", "    try:\n", "        execute_nl(t)\n", "    except SystemExit as e:\n", "        print(\"[ERROR]\", e)\n"]}], "metadata": {"kernelspec": {"display_name": "llamacpp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}