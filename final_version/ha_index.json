{"by_entity": {"humidifier.humidifier": {"friendly_name": "Humidifier", "domain": "humidifier", "services": ["turn_on", "turn_off", "set_humidity"], "args": {"set_humidity": ["humidity"]}}, "light.bed_light": {"friendly_name": "Bed Light", "domain": "light", "services": ["turn_on", "turn_off"], "args": {"turn_on": ["brightness_pct", "color_temp_kelvin"]}}, "light.ceiling_lights": {"friendly_name": "Ceiling Lights", "domain": "light", "services": ["turn_on", "turn_off"], "args": {"turn_on": ["brightness_pct", "color_temp_kelvin"]}}, "light.kitchen_lights": {"friendly_name": "Kitchen Lights", "domain": "light", "services": ["turn_on", "turn_off"], "args": {"turn_on": ["brightness_pct", "color_temp_kelvin"]}}, "fan.ceiling_fan": {"friendly_name": "Ceiling Fan", "domain": "fan", "services": ["turn_on", "turn_off", "set_percentage"], "args": {"set_percentage": ["percentage"]}}, "fan.living_room_fan": {"friendly_name": "Living Room Fan", "domain": "fan", "services": ["turn_on", "turn_off", "set_percentage"], "args": {"set_percentage": ["percentage"]}}, "vacuum.demo_vacuum_0_ground_floor": {"friendly_name": "Demo vacuum 0 ground floor", "domain": "vacuum", "services": ["start", "pause", "stop", "return_to_base", "set_fan_speed"], "args": {"set_fan_speed": ["fan_speed"]}}, "lock.front_door": {"friendly_name": "Front Door", "domain": "lock", "services": ["lock", "unlock"]}, "lock.kitchen_door": {"friendly_name": "Kitchen Door", "domain": "lock", "services": ["lock", "unlock"]}, "sensor.outside_temperature": {"friendly_name": "Outside Temperature", "domain": "sensor", "services": []}, "sensor.carbon_dioxide": {"friendly_name": "Carbon Dioxide", "domain": "sensor", "services": []}, "cover.kitchen_window": {"friendly_name": "Kitchen Window", "domain": "cover", "services": ["open_cover", "close_cover", "stop_cover"]}, "cover.living_room_window": {"friendly_name": "Living Room Window", "domain": "cover", "services": ["open_cover", "close_cover", "stop_cover", "set_cover_position", "set_cover_tilt_position"], "args": {"set_cover_position": ["position"], "set_cover_tilt_position": ["tilt_position"]}}, "siren.siren": {"friendly_name": "<PERSON><PERSON>", "domain": "siren", "services": ["turn_on", "turn_off"], "args": {"turn_on": ["tone", "volume_level"]}}}, "by_name": {"humidifier": ["humidifier.humidifier"], "bed light": ["light.bed_light"], "ceiling lights": ["light.ceiling_lights"], "kitchen lights": ["light.kitchen_lights"], "ceiling fan": ["fan.ceiling_fan"], "living room fan": ["fan.living_room_fan"], "vacuum": ["vacuum.demo_vacuum_0_ground_floor"], "front door": ["lock.front_door"], "kitchen door": ["lock.kitchen_door"], "outside temperature": ["sensor.outside_temperature"], "carbon dioxide": ["sensor.carbon_dioxide"], "kitchen window": ["cover.kitchen_window"], "living room window": ["cover.living_room_window"], "siren": ["siren.siren"]}}