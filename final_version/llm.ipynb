{"cells": [{"cell_type": "code", "execution_count": null, "id": "e659e68a", "metadata": {}, "outputs": [], "source": ["# ==== 基本配置（按需修改）====\n", "import os, json, requests, textwrap\n", "from datetime import datetime\n", "\n", "HA_URL   = os.getenv(\"HA_URL\",   \"http://**************:8123\").rstrip(\"/\")\n", "HA_TOKEN = os.getenv(\"HA_TOKEN\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2ZmI5MTgwMjQzYWM0MmVlYTAyMzMwZWM0YWZmYzEzYyIsImlhdCI6MTc1ODAwMzY0NywiZXhwIjoyMDczMzYzNjQ3fQ.RGPLPOkRo03xAaRgNF5dD_2KDuBa0UO9aaSbjp0X6DA\")  # 也可以 export HA_TOKEN=... 后不改这里\n", "\n", "TIMEOUT  = 15\n", "\n", "def _headers_json():\n", "    return {\"Authorization\": f\"Bearer {HA_TOKEN}\", \"Content-Type\": \"application/json\"}\n", "\n", "def _headers_auth():\n", "    return {\"Authorization\": f\"Bearer {HA_TOKEN}\"}\n", "\n", "def jprint(obj):\n", "    print(json.dumps(obj, ensure_ascii=False, indent=2))\n"]}, {"cell_type": "code", "execution_count": null, "id": "5d09317f", "metadata": {}, "outputs": [], "source": ["def call_service(domain: str, service: str, entity_id: str, **kwargs):\n", "    url  = f\"{HA_URL}/api/services/{domain}/{service}\"\n", "    body = {\"entity_id\": entity_id}; body.update(kwargs or {})\n", "    r = requests.post(url, headers=_headers_json(), json=body, timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    try:\n", "        return r.json()\n", "    except ValueError:\n", "        return {\"ok\": True, \"status_code\": r.status_code, \"text\": r.text}\n", "\n", "def get_state(entity_id: str):\n", "    url = f\"{HA_URL}/api/states/{entity_id}\"\n", "    r = requests.get(url, headers=_headers_auth(), timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    return r.json()\n", "\n", "def run_cmd(cmd: dict, check_state: bool = True):\n", "    \"\"\"\n", "    cmd 形如：\n", "      {\"domain\":\"light\",\"service\":\"turn_on\",\"entity_id\":\"light.bed_light\",\"args\":{\"brightness_pct\":80}}\n", "    \"\"\"\n", "    domain    = cmd[\"domain\"]\n", "    service   = cmd[\"service\"]\n", "    entity_id = cmd[\"entity_id\"]\n", "    args      = cmd.get(\"args\") or {}\n", "\n", "    # 自动修正 domain\n", "    prefix = entity_id.split(\".\", 1)[0]\n", "    if prefix != domain:\n", "        print(f\"[WARN] domain={domain} 与 entity_id 前缀={prefix} 不一致 → 已修正为 {prefix}\")\n", "        domain = prefix\n", "\n", "    print(\"=== 等价 curl ===\")\n", "    body = {\"entity_id\": entity_id}; body.update(args)\n", "    curl = (\n", "        f\"curl -X POST '{HA_URL}/api/services/{domain}/{service}' \"\n", "        \"-H 'Content-Type: application/json' \"\n", "        \"-H 'Authorization: Bearer $HA_TOKEN' \"\n", "        f\"-d '{json.dumps(body, ensure_ascii=False)}'\"\n", "    )\n", "    print(curl)\n", "\n", "    print(\"\\n=== 执行服务 ===\")\n", "    resp = call_service(domain, service, entity_id, **args)\n", "    jprint(resp if resp else {\"ok\": True, \"note\": \"服务已调用，无 JSON 返回\"})\n", "\n", "    if check_state:\n", "        try:\n", "            st = get_state(entity_id)\n", "            print(\"\\n=== 最新状态 ===\")\n", "            jprint(st)\n", "        except Exception as e:\n", "            print(f\"[WARN] 状态查询失败: {e}\")\n", "    return resp\n"]}, {"cell_type": "code", "execution_count": null, "id": "bded9ad6", "metadata": {}, "outputs": [], "source": ["from llama_cpp import Llama, LlamaGrammar\n", "\n", "# 模型路径（换成你的实际路径）\n", "MODEL_PATH = \"/home/<USER>/LLM_Models/Qwen2.5-0.5B-Instruct-GGUF/qwen2.5-0.5b-instruct-fp16.gguf\"\n", "\n", "llm = Llama(\n", "    model_path=MODEL_PATH,\n", "    n_threads=8,\n", "    n_ctx=2048\n", ")\n", "\n", "print(\"✅ 模型已加载\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "5c6e7fe3", "metadata": {}, "outputs": [], "source": ["def format_chat_qwen(messages):\n", "    parts = []\n", "    for m in messages:\n", "        if m[\"role\"] == \"system\":\n", "            parts.append(f\"<|im_start|>system\\n{m['content']}<|im_end|>\\n\")\n", "        elif m[\"role\"] == \"user\":\n", "            parts.append(f\"<|im_start|>user\\n{m['content']}<|im_end|>\\n\")\n", "        elif m[\"role\"] == \"assistant\":\n", "            parts.append(f\"<|im_start|>assistant\\n{m['content']}<|im_end|>\\n\")\n", "    parts.append(\"<|im_start|>assistant\\n\")\n", "    return \"\".join(parts)\n", "\n", "# 简化版 GBNF，只约束输出为一个 JSON 对象\n", "GBNF = r'''\n", "ws ::= [ \\t\\n\\r]*\n", "root ::= ws object ws\n", "object ::= \"{\" ws pair (ws \",\" ws pair)* ws \"}\"\n", "pair ::= string ws \":\" ws value\n", "string ::= \"\\\"\" [^\\\"]* \"\\\"\"\n", "value ::= string | number | object | array | \"true\" | \"false\" | \"null\"\n", "array ::= \"[\" ws (value (ws \",\" ws value)*)? ws \"]\"\n", "number ::= \"-\"? [0-9]+ (\".\" [0-9]+)?\n", "'''\n", "grammar = LlamaGrammar.from_string(GBNF)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9b11e379", "metadata": {}, "outputs": [], "source": ["def llm_to_cmd(user_text: str):\n", "    SYSTEM = (\n", "        \"你是 Home Assistant 控制器，只输出一个 JSON：\"\n", "        '{\"domain\":\"...\",\"service\":\"...\",\"entity_id\":\"...\",\"args\":{...}}。\\n'\n", "        \"如果有模板后端设备（如 input_boolean），优先使用。\\n\"\n", "        \"亮度用 brightness_pct(0-100)，不要用 brightness。\"\n", "    )\n", "\n", "    prompt = format_chat_qwen([\n", "        {\"role\":\"system\",\"content\": SYSTEM},\n", "        {\"role\":\"user\",\"content\": user_text}\n", "    ])\n", "\n", "    out = llm.create_completion(\n", "        prompt=prompt,\n", "        max_tokens=128,\n", "        temperature=0,\n", "        grammar=grammar,\n", "        stop=[\"<|im_end|>\"]\n", "    )\n", "\n", "    raw = out[\"choices\"][0][\"text\"].strip()\n", "    print(\"RAW:\", raw)\n", "    cmd = json.loads(raw)\n", "\n", "    # 自动修正 domain / 清理非法 args\n", "    eid = cmd.get(\"entity_id\",\"\")\n", "    if \".\" in eid:\n", "        prefix = eid.split(\".\",1)[0]\n", "        cmd[\"domain\"] = prefix\n", "    return cmd\n", "\n", "# 🔥 测试\n", "user_text = \"打开卧室灯，亮度调到 70%\"\n", "cmd = llm_to_cmd(user_text)\n", "print(\"解析结果:\", cmd)\n", "\n", "# 真正执行\n", "run_cmd(cmd)\n"]}, {"cell_type": "code", "execution_count": null, "id": "b9e4aae6", "metadata": {}, "outputs": [], "source": ["# 直接内嵌你现在的 ha_index（也可以改成从文件读取）\n", "HA_INDEX = {\n", "  \"by_entity\": {\n", "    \"humidifier.humidifier\": {\n", "      \"friendly_name\": \"Humidifier\",\n", "      \"domain\": \"humidifier\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_humidity\"],\n", "      \"args\": { \"set_humidity\": [\"humidity\"] }\n", "    },\n", "    \"light.bed_light\": {\n", "      \"friendly_name\": \"Bed Light\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\", \"color_temp_kelvin\"] }\n", "    },\n", "    \"light.ceiling_lights\": {\n", "      \"friendly_name\": \"Ceiling Lights\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\"] }\n", "    },\n", "    \"light.kitchen_lights\": {\n", "      \"friendly_name\": \"Kitchen Lights\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\"] }\n", "    },\n", "    \"fan.ceiling_fan\": {\n", "      \"friendly_name\": \"Ceiling Fan\",\n", "      \"domain\": \"fan\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_percentage\"],\n", "      \"args\": { \"set_percentage\": [\"percentage\"] }\n", "    },\n", "    \"fan.living_room_fan\": {\n", "      \"friendly_name\": \"Living Room Fan\",\n", "      \"domain\": \"fan\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_percentage\"],\n", "      \"args\": { \"set_percentage\": [\"percentage\"] }\n", "    },\n", "    \"vacuum.demo_vacuum_0_ground_floor\": {\n", "      \"friendly_name\": \"Demo vacuum 0 ground floor\",\n", "      \"domain\": \"vacuum\",\n", "      \"services\": [\"start\", \"pause\", \"stop\", \"return_to_base\", \"set_fan_speed\"],\n", "      \"args\": { \"set_fan_speed\": [\"fan_speed\"] }\n", "    },\n", "    \"lock.front_door\": {\n", "      \"friendly_name\": \"Front Door\",\n", "      \"domain\": \"lock\",\n", "      \"services\": [\"lock\", \"unlock\"]\n", "    },\n", "    \"lock.kitchen_door\": {\n", "      \"friendly_name\": \"Kitchen Door\",\n", "      \"domain\": \"lock\",\n", "      \"services\": [\"lock\", \"unlock\"]\n", "    },\n", "    \"sensor.outside_temperature\": {\n", "      \"friendly_name\": \"Outside Temperature\",\n", "      \"domain\": \"sensor\",\n", "      \"services\": []\n", "    },\n", "    \"sensor.carbon_dioxide\": {\n", "      \"friendly_name\": \"Carbon Dioxide\",\n", "      \"domain\": \"sensor\",\n", "      \"services\": []\n", "    },\n", "    \"cover.kitchen_window\": {\n", "      \"friendly_name\": \"Kitchen Window\",\n", "      \"domain\": \"cover\",\n", "      \"services\": [\"open_cover\", \"close_cover\", \"stop_cover\"]\n", "    },\n", "    \"cover.living_room_window\": {\n", "      \"friendly_name\": \"Living Room Window\",\n", "      \"domain\": \"cover\",\n", "      \"services\": [\"open_cover\", \"close_cover\", \"stop_cover\"]\n", "    },\n", "    \"siren.siren\": {\n", "      \"friendly_name\": \"<PERSON><PERSON>\",\n", "      \"domain\": \"siren\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"tone\", \"volume_level\"] }\n", "    }\n", "  },\n", "  \"by_name\": {\n", "    \"humidifier\": [\"humidifier.humidifier\"],\n", "    \"bed light\": [\"light.bed_light\"],\n", "    \"ceiling lights\": [\"light.ceiling_lights\"],\n", "    \"kitchen lights\": [\"light.kitchen_lights\"],\n", "    \"ceiling fan\": [\"fan.ceiling_fan\"],\n", "    \"living room fan\": [\"fan.living_room_fan\"],\n", "    \"vacuum\": [\"vacuum.demo_vacuum_0_ground_floor\"],\n", "    \"front door\": [\"lock.front_door\"],\n", "    \"kitchen door\": [\"lock.kitchen_door\"],\n", "    \"outside temperature\": [\"sensor.outside_temperature\"],\n", "    \"carbon dioxide\": [\"sensor.carbon_dioxide\"],\n", "    \"kitchen window\": [\"cover.kitchen_window\"],\n", "    \"living room window\": [\"cover.living_room_window\"],\n", "    \"siren\": [\"siren.siren\"]\n", "  }\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "id": "7341f764", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "# 域名与服务名的容错映射（把 LLM 的“口误”纠成 HA 的真服务）\n", "DOMAIN_MAP = {\n", "  \"lights\": \"light\",\n", "  \"light\": \"light\",\n", "  \"fans\": \"fan\",\n", "  \"switches\": \"switch\",\n", "  \"locks\": \"lock\",\n", "  \"covers\": \"cover\",\n", "  \"sirens\": \"siren\",\n", "  \"humidifiers\": \"humidifier\",\n", "  \"vacuums\": \"vacuum\",\n", "}\n", "\n", "SERVICE_MAP = {\n", "  \"light\": {\n", "    \"set_brightness\": \"turn_on\",\n", "    \"set_brightness_pct\": \"turn_on\",\n", "    \"brightness\": \"turn_on\",\n", "    \"on\": \"turn_on\",\n", "    \"off\": \"turn_off\",\n", "    \"toggle\": \"toggle\",\n", "    \"turn_on\": \"turn_on\",\n", "    \"turn_off\": \"turn_off\",\n", "  },\n", "  \"fan\": {\n", "    \"set_speed\": \"set_percentage\",\n", "    \"set_fan_speed\": \"set_percentage\",\n", "    \"speed\": \"set_percentage\",\n", "    \"on\": \"turn_on\",\n", "    \"off\": \"turn_off\",\n", "    \"turn_on\": \"turn_on\",\n", "    \"turn_off\": \"turn_off\",\n", "    \"set_percentage\": \"set_percentage\",\n", "  },\n", "  \"cover\": {\n", "    \"open\": \"open_cover\",\n", "    \"close\": \"close_cover\",\n", "    \"stop\": \"stop_cover\",\n", "    \"open_cover\": \"open_cover\",\n", "    \"close_cover\": \"close_cover\",\n", "    \"stop_cover\": \"stop_cover\",\n", "  },\n", "  \"lock\": {\n", "    \"open\": \"unlock\",\n", "    \"close\": \"lock\",\n", "    \"lock\": \"lock\",\n", "    \"unlock\": \"unlock\",\n", "  },\n", "  \"humidifier\": {\n", "    \"set\": \"set_humidity\",\n", "    \"set_humidity\": \"set_humidity\",\n", "    \"turn_on\": \"turn_on\",\n", "    \"turn_off\": \"turn_off\",\n", "  },\n", "  \"vacuum\": {\n", "    \"start\": \"start\",\n", "    \"pause\": \"pause\",\n", "    \"stop\": \"stop\",\n", "    \"dock\": \"return_to_base\",\n", "    \"return\": \"return_to_base\",\n", "    \"return_to_base\": \"return_to_base\",\n", "    \"set_fan_speed\": \"set_fan_speed\",\n", "  },\n", "  \"siren\": {\n", "    \"on\": \"turn_on\",\n", "    \"off\": \"turn_off\",\n", "    \"turn_on\": \"turn_on\",\n", "    \"turn_off\": \"turn_off\",\n", "  },\n", "}\n", "\n", "def _first(iterable, default=None):\n", "  for x in iterable:\n", "    return x\n", "  return default\n", "\n", "def resolve_entity_id(text: str) -> str | None:\n", "  \"\"\"把 LLM 给的 entity_id/别名/友好名，解析成真实 entity_id。\"\"\"\n", "  text_norm = text.strip().lower()\n", "  # 1) 如果已经是 entity_id 形式 domain.object\n", "  if \".\" in text_norm and re.match(r\"^[a-z_]+\\.[a-z0-9_]+$\", text_norm):\n", "    if text_norm in HA_INDEX[\"by_entity\"]:\n", "      return text_norm\n", "  # 2) 试 by_name 精确匹配\n", "  if text_norm in HA_INDEX.get(\"by_name\", {}):\n", "    return _first(HA_INDEX[\"by_name\"][text_norm])\n", "  # 3) 试按 friendly_name 子串搜索\n", "  for eid, meta in HA_INDEX[\"by_entity\"].items():\n", "    fn = (meta.get(\"friendly_name\") or \"\").strip().lower()\n", "    if fn and text_norm in fn:\n", "      return eid\n", "  return None\n", "\n", "def normalize_cmd(cmd: dict) -> dict:\n", "  \"\"\"修正 LLM 输出：域/服务/实体/参数，且用 ha_index 白名单校验。\"\"\"\n", "  domain_in = cmd.get(\"domain\",\"\").strip().lower()\n", "  service_in = cmd.get(\"service\",\"\").strip().lower()\n", "  eid_in = cmd.get(\"entity_id\",\"\").strip()\n", "  args_in = cmd.get(\"args\") or {}\n", "\n", "  # 先把 entity_id 解析正确（允许传 alias / 友好名）\n", "  eid = resolve_entity_id(eid_in) or eid_in  # 若失败，先保留原样，后面再校验\n", "\n", "  # 用 entity_id 的前缀确定真 domain\n", "  if \".\" in eid:\n", "    true_domain = eid.split(\".\",1)[0]\n", "  else:\n", "    # 没有 '.' 说明 LLM 给的是别名且没命中，直接报错更好\n", "    raise SystemExit(f\"无法解析实体 '{eid_in}' → 请检查 ha_index by_name 或改用真实 entity_id\")\n", "\n", "  # 规范 domain：优先用实体前缀，其次用映射\n", "  domain = true_domain\n", "  # 映射 service 到标准名\n", "  service_map = SERVICE_MAP.get(domain, {})\n", "  service_std = service_map.get(service_in, service_in)\n", "\n", "  # 白名单校验：实体存在\n", "  meta = HA_INDEX[\"by_entity\"].get(eid)\n", "  if not meta:\n", "    raise SystemExit(f\"实体不存在或不在白名单: {eid}\")\n", "\n", "  # 白名单校验：服务允许\n", "  allowed_svcs = set(meta.get(\"services\", []))\n", "  if service_std not in allowed_svcs:\n", "    # 特殊纠错：light 的 set_brightness → turn_on\n", "    if domain == \"light\" and service_in in (\"set_brightness\",\"brightness\",\"set_brightness_pct\"):\n", "      service_std = \"turn_on\"\n", "    else:\n", "      raise SystemExit(f\"服务不被允许: {domain}.{service_std} for {eid}; 允许: {sorted(allowed_svcs)}\")\n", "\n", "  # 过滤/修正参数\n", "  allowed_args = set(meta.get(\"args\", {}).get(service_std, []))\n", "  args = {}\n", "  for k, v in (args_in or {}).items():\n", "    if k in allowed_args:\n", "      args[k] = v\n", "\n", "  # 参数进一步归一\n", "  if domain == \"light\" and service_std == \"turn_on\":\n", "    # brightness_pct 0-100 截断\n", "    if \"brightness_pct\" in args:\n", "      try:\n", "        args[\"brightness_pct\"] = max(0, min(100, int(args[\"brightness_pct\"])))\n", "      except Exception:\n", "        args.pop(\"brightness_pct\", None)\n", "    # 色温可保留不改（若不支持，HA 会忽略）\n", "\n", "  if domain == \"fan\" and service_std == \"set_percentage\":\n", "    try:\n", "      args[\"percentage\"] = max(0, min(100, int(args.get(\"percentage\", 0))))\n", "    except Exception:\n", "      raise SystemExit(\"fan.set_percentage 需要整数 percentage 0-100\")\n", "\n", "  if domain == \"humidifier\" and service_std == \"set_humidity\":\n", "    try:\n", "      args[\"humidity\"] = max(0, min(100, int(args.get(\"humidity\", 0))))\n", "    except Exception:\n", "      raise SystemExit(\"humidifier.set_humidity 需要整数 humidity 0-100\")\n", "\n", "  if domain == \"vacuum\" and service_std == \"set_fan_speed\":\n", "    # 保留传入值，是否合法由设备决定；也可在这里和 fan_speed_list 交叉校验（若你愿意可补）\n", "    pass\n", "\n", "  return {\"domain\": domain, \"service\": service_std, \"entity_id\": eid, \"args\": args}\n"]}, {"cell_type": "code", "execution_count": null, "id": "c40d154f", "metadata": {}, "outputs": [], "source": ["def run_cmd_safe(cmd: dict, check_state: bool = True):\n", "  \"\"\"在原 run_cmd 的基础上，先做 normalize + 白名单校验，再执行。\"\"\"\n", "  fixed = normalize_cmd(cmd)\n", "\n", "  # 打印等价 curl\n", "  print(\"=== 规范化指令 ===\")\n", "  print(json.dumps(fixed, ensure_ascii=False, indent=2))\n", "  print(\"\\n=== 等价 curl ===\")\n", "  body = {\"entity_id\": fixed[\"entity_id\"]}; body.update(fixed.get(\"args\") or {})\n", "  curl = (\n", "      f\"curl -X POST '{HA_URL}/api/services/{fixed['domain']}/{fixed['service']}' \"\n", "      \"-H 'Content-Type: application/json' \"\n", "      \"-H 'Authorization: Bearer $HA_TOKEN' \"\n", "      f\"-d '{json.dumps(body, ensure_ascii=False)}'\"\n", "  )\n", "  print(curl)\n", "\n", "  # 真正执行\n", "  resp = call_service(fixed[\"domain\"], fixed[\"service\"], fixed[\"entity_id\"], **(fixed.get(\"args\") or {}))\n", "  try:\n", "    print(\"\\n=== 返回 ===\")\n", "    print(json.dumps(resp, ensure_ascii=False, indent=2))\n", "  except Exception:\n", "    print(resp)\n", "\n", "  if check_state:\n", "    try:\n", "      st = get_state(fixed[\"entity_id\"])\n", "      print(\"\\n=== 最新状态 ===\")\n", "      print(json.dumps(st, ensure_ascii=False, indent=2))\n", "    except Exception as e:\n", "      print(f\"[WARN] 读取状态失败：{e}\")\n", "\n", "  return fixed, resp\n"]}, {"cell_type": "code", "execution_count": null, "id": "b89f0d51", "metadata": {}, "outputs": [], "source": ["# 你 LLM 刚才给的错误输出：\n", "bad = {\"domain\":\"lights\",\"service\":\"set_brightness\",\"entity_id\":\"brightness_0\",\"args\":{\"brightness_pct\":70}}\n", "\n", "# 我们只需要把 entity_id 指成正确的：比如想控制 Bed Light\n", "bad[\"entity_id\"] = \"bed light\"   # 可以用 by_name 里的别名 / 友好名\n", "\n", "fixed, resp = run_cmd_safe(bad, check_state=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "7ceb1cb2", "metadata": {}, "outputs": [], "source": ["# 1) 风扇 65%\n", "cmd1 = {\"domain\":\"fans\",\"service\":\"set_speed\",\"entity_id\":\"living room fan\",\"args\":{\"percentage\":65}}\n", "run_cmd_safe(cmd1)\n", "\n", "# 2) 窗帘关闭（LLM 说 'close'）\n", "cmd2 = {\"domain\":\"cover\",\"service\":\"close\",\"entity_id\":\"kitchen window\",\"args\":{}}\n", "run_cmd_safe(cmd2)\n"]}, {"cell_type": "code", "execution_count": 25, "id": "ddffcf23", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["llama_model_loader: loaded meta data with 26 key-value pairs and 291 tensors from /home/<USER>/LLM_Models/Qwen2.5-0.5B-Instruct-GGUF/qwen2.5-0.5b-instruct-fp16.gguf (version GGUF V3 (latest))\n", "llama_model_loader: Dumping metadata keys/values. Note: KV overrides do not apply in this output.\n", "llama_model_loader: - kv   0:                       general.architecture str              = qwen2\n", "llama_model_loader: - kv   1:                               general.type str              = model\n", "llama_model_loader: - kv   2:                               general.name str              = qwen2.5-0.5b-instruct\n", "llama_model_loader: - kv   3:                            general.version str              = v0.1\n", "llama_model_loader: - kv   4:                           general.finetune str              = qwen2.5-0.5b-instruct\n", "llama_model_loader: - kv   5:                         general.size_label str              = 630M\n", "llama_model_loader: - kv   6:                          qwen2.block_count u32              = 24\n", "llama_model_loader: - kv   7:                       qwen2.context_length u32              = 8192\n", "llama_model_loader: - kv   8:                     qwen2.embedding_length u32              = 896\n", "llama_model_loader: - kv   9:                  qwen2.feed_forward_length u32              = 4864\n", "llama_model_loader: - kv  10:                 qwen2.attention.head_count u32              = 14\n", "llama_model_loader: - kv  11:              qwen2.attention.head_count_kv u32              = 2\n", "llama_model_loader: - kv  12:                       qwen2.rope.freq_base f32              = 1000000.000000\n", "llama_model_loader: - kv  13:     qwen2.attention.layer_norm_rms_epsilon f32              = 0.000001\n", "llama_model_loader: - kv  14:                          general.file_type u32              = 1\n", "llama_model_loader: - kv  15:                       tokenizer.ggml.model str              = gpt2\n", "llama_model_loader: - kv  16:                         tokenizer.ggml.pre str              = qwen2\n", "llama_model_loader: - kv  17:                      tokenizer.ggml.tokens arr[str,151936]  = [\"!\", \"\\\"\", \"#\", \"$\", \"%\", \"&\", \"'\", ...\n", "llama_model_loader: - kv  18:                  tokenizer.ggml.token_type arr[i32,151936]  = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, ...\n", "llama_model_loader: - kv  19:                      tokenizer.ggml.merges arr[str,151387]  = [\"Ġ Ġ\", \"ĠĠ ĠĠ\", \"i n\", \"Ġ t\",...\n", "llama_model_loader: - kv  20:                tokenizer.ggml.eos_token_id u32              = 151645\n", "llama_model_loader: - kv  21:            tokenizer.ggml.padding_token_id u32              = 151643\n", "llama_model_loader: - kv  22:                tokenizer.ggml.bos_token_id u32              = 151643\n", "llama_model_loader: - kv  23:               tokenizer.ggml.add_bos_token bool             = false\n", "llama_model_loader: - kv  24:                    tokenizer.chat_template str              = {%- if tools %}\\n    {{- '<|im_start|>...\n", "llama_model_loader: - kv  25:               general.quantization_version u32              = 2\n", "llama_model_loader: - type  f32:  121 tensors\n", "llama_model_loader: - type  f16:  170 tensors\n", "print_info: file format = GGUF V3 (latest)\n", "print_info: file type   = F16\n", "print_info: file size   = 1.17 GiB (16.00 BPW) \n", "init_tokenizer: initializing tokenizer for type 2\n", "load: control token: 151660 '<|fim_middle|>' is not marked as EOG\n", "load: control token: 151659 '<|fim_prefix|>' is not marked as EOG\n", "load: control token: 151653 '<|vision_end|>' is not marked as EOG\n", "load: control token: 151648 '<|box_start|>' is not marked as EOG\n", "load: control token: 151646 '<|object_ref_start|>' is not marked as EOG\n", "load: control token: 151649 '<|box_end|>' is not marked as EOG\n", "load: control token: 151655 '<|image_pad|>' is not marked as EOG\n", "load: control token: 151651 '<|quad_end|>' is not marked as EOG\n", "load: control token: 151647 '<|object_ref_end|>' is not marked as EOG\n", "load: control token: 151652 '<|vision_start|>' is not marked as EOG\n", "load: control token: 151654 '<|vision_pad|>' is not marked as EOG\n", "load: control token: 151656 '<|video_pad|>' is not marked as EOG\n", "load: control token: 151644 '<|im_start|>' is not marked as EOG\n", "load: control token: 151661 '<|fim_suffix|>' is not marked as EOG\n", "load: control token: 151650 '<|quad_start|>' is not marked as EOG\n", "load: printing all EOG tokens:\n", "load:   - 151643 ('<|endoftext|>')\n", "load:   - 151645 ('<|im_end|>')\n", "load:   - 151662 ('<|fim_pad|>')\n", "load:   - 151663 ('<|repo_name|>')\n", "load:   - 151664 ('<|file_sep|>')\n", "load: special tokens cache size = 22\n", "load: token to piece cache size = 0.9310 MB\n", "print_info: arch             = qwen2\n", "print_info: vocab_only       = 0\n", "print_info: n_ctx_train      = 8192\n", "print_info: n_embd           = 896\n", "print_info: n_layer          = 24\n", "print_info: n_head           = 14\n", "print_info: n_head_kv        = 2\n", "print_info: n_rot            = 64\n", "print_info: n_swa            = 0\n", "print_info: is_swa_any       = 0\n", "print_info: n_embd_head_k    = 64\n", "print_info: n_embd_head_v    = 64\n", "print_info: n_gqa            = 7\n", "print_info: n_embd_k_gqa     = 128\n", "print_info: n_embd_v_gqa     = 128\n", "print_info: f_norm_eps       = 0.0e+00\n", "print_info: f_norm_rms_eps   = 1.0e-06\n", "print_info: f_clamp_kqv      = 0.0e+00\n", "print_info: f_max_alibi_bias = 0.0e+00\n", "print_info: f_logit_scale    = 0.0e+00\n", "print_info: f_attn_scale     = 0.0e+00\n", "print_info: n_ff             = 4864\n", "print_info: n_expert         = 0\n", "print_info: n_expert_used    = 0\n", "print_info: causal attn      = 1\n", "print_info: pooling type     = -1\n", "print_info: rope type        = 2\n", "print_info: rope scaling     = linear\n", "print_info: freq_base_train  = 1000000.0\n", "print_info: freq_scale_train = 1\n", "print_info: n_ctx_orig_yarn  = 8192\n", "print_info: rope_finetuned   = unknown\n", "print_info: model type       = 1B\n", "print_info: model params     = 630.17 M\n", "print_info: general.name     = qwen2.5-0.5b-instruct\n", "print_info: vocab type       = BPE\n", "print_info: n_vocab          = 151936\n", "print_info: n_merges         = 151387\n", "print_info: BOS token        = 151643 '<|endoftext|>'\n", "print_info: EOS token        = 151645 '<|im_end|>'\n", "print_info: EOT token        = 151645 '<|im_end|>'\n", "print_info: PAD token        = 151643 '<|endoftext|>'\n", "print_info: LF token         = 198 'Ċ'\n", "print_info: FIM PRE token    = 151659 '<|fim_prefix|>'\n", "print_info: FIM SUF token    = 151661 '<|fim_suffix|>'\n", "print_info: FIM MID token    = 151660 '<|fim_middle|>'\n", "print_info: FIM PAD token    = 151662 '<|fim_pad|>'\n", "print_info: FIM REP token    = 151663 '<|repo_name|>'\n", "print_info: FIM SEP token    = 151664 '<|file_sep|>'\n", "print_info: EOG token        = 151643 '<|endoftext|>'\n", "print_info: EOG token        = 151645 '<|im_end|>'\n", "print_info: EOG token        = 151662 '<|fim_pad|>'\n", "print_info: EOG token        = 151663 '<|repo_name|>'\n", "print_info: EOG token        = 151664 '<|file_sep|>'\n", "print_info: max token length = 256\n", "load_tensors: loading model tensors, this can take a while... (mmap = true)\n", "load_tensors: layer   0 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   1 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   2 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   3 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   4 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   5 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   6 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   7 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   8 assigned to device CPU, is_swa = 0\n", "load_tensors: layer   9 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  10 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  11 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  12 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  13 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  14 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  15 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  16 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  17 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  18 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  19 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  20 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  21 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  22 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  23 assigned to device CPU, is_swa = 0\n", "load_tensors: layer  24 assigned to device CPU, is_swa = 0\n", "load_tensors: tensor 'token_embd.weight' (f16) (and 290 others) cannot be used with preferred buffer type CPU_REPACK, using CPU instead\n", "load_tensors:   CPU_Mapped model buffer size =  1202.09 MiB\n", "...........................................................\n", "llama_context: constructing llama_context\n", "llama_context: n_seq_max     = 1\n", "llama_context: n_ctx         = 2048\n", "llama_context: n_ctx_per_seq = 2048\n", "llama_context: n_batch       = 512\n", "llama_context: n_ubatch      = 512\n", "llama_context: causal_attn   = 1\n", "llama_context: flash_attn    = 0\n", "llama_context: kv_unified    = false\n", "llama_context: freq_base     = 1000000.0\n", "llama_context: freq_scale    = 1\n", "llama_context: n_ctx_per_seq (2048) < n_ctx_train (8192) -- the full capacity of the model will not be utilized\n", "set_abort_callback: call\n", "llama_context:        CPU  output buffer size =     0.58 MiB\n", "create_memory: n_ctx = 2048 (padded)\n", "llama_kv_cache_unified: layer   0: dev = CPU\n", "llama_kv_cache_unified: layer   1: dev = CPU\n", "llama_kv_cache_unified: layer   2: dev = CPU\n", "llama_kv_cache_unified: layer   3: dev = CPU\n", "llama_kv_cache_unified: layer   4: dev = CPU\n", "llama_kv_cache_unified: layer   5: dev = CPU\n", "llama_kv_cache_unified: layer   6: dev = CPU\n", "llama_kv_cache_unified: layer   7: dev = CPU\n", "llama_kv_cache_unified: layer   8: dev = CPU\n", "llama_kv_cache_unified: layer   9: dev = CPU\n", "llama_kv_cache_unified: layer  10: dev = CPU\n", "llama_kv_cache_unified: layer  11: dev = CPU\n", "llama_kv_cache_unified: layer  12: dev = CPU\n", "llama_kv_cache_unified: layer  13: dev = CPU\n", "llama_kv_cache_unified: layer  14: dev = CPU\n", "llama_kv_cache_unified: layer  15: dev = CPU\n", "llama_kv_cache_unified: layer  16: dev = CPU\n", "llama_kv_cache_unified: layer  17: dev = CPU\n", "llama_kv_cache_unified: layer  18: dev = CPU\n", "llama_kv_cache_unified: layer  19: dev = CPU\n", "llama_kv_cache_unified: layer  20: dev = CPU\n", "llama_kv_cache_unified: layer  21: dev = CPU\n", "llama_kv_cache_unified: layer  22: dev = CPU\n", "llama_kv_cache_unified: layer  23: dev = CPU\n", "llama_kv_cache_unified:        CPU KV buffer size =    24.00 MiB\n", "llama_kv_cache_unified: size =   24.00 MiB (  2048 cells,  24 layers,  1/1 seqs), K (f16):   12.00 MiB, V (f16):   12.00 MiB\n", "llama_context: enumerating backends\n", "llama_context: backend_ptrs.size() = 2\n", "llama_context: max_nodes = 2328\n", "llama_context: worst-case: n_tokens = 512, n_seqs = 1, n_outputs = 0\n", "graph_reserve: reserving a graph for ubatch with n_tokens =  512, n_seqs =  1, n_outputs =  512\n", "graph_reserve: reserving a graph for ubatch with n_tokens =    1, n_seqs =  1, n_outputs =    1\n", "graph_reserve: reserving a graph for ubatch with n_tokens =  512, n_seqs =  1, n_outputs =  512\n", "llama_context:        CPU compute buffer size =   298.50 MiB\n", "llama_context: graph nodes  = 918\n", "llama_context: graph splits = 338 (with bs=512), 1 (with bs=1)\n", "CPU : SSE3 = 1 | LLAMAFILE = 1 | OPENMP = 1 | REPACK = 1 | \n", "Model metadata: {'tokenizer.ggml.add_bos_token': 'false', 'tokenizer.ggml.bos_token_id': '151643', 'general.file_type': '1', 'qwen2.attention.layer_norm_rms_epsilon': '0.000001', 'general.architecture': 'qwen2', 'tokenizer.ggml.padding_token_id': '151643', 'qwen2.embedding_length': '896', 'tokenizer.ggml.pre': 'qwen2', 'general.name': 'qwen2.5-0.5b-instruct', 'qwen2.block_count': '24', 'general.version': 'v0.1', 'tokenizer.ggml.eos_token_id': '151645', 'qwen2.rope.freq_base': '1000000.000000', 'general.finetune': 'qwen2.5-0.5b-instruct', 'general.type': 'model', 'general.size_label': '630M', 'qwen2.context_length': '8192', 'tokenizer.chat_template': '{%- if tools %}\\n    {{- \\'<|im_start|>system\\\\n\\' }}\\n    {%- if messages[0][\\'role\\'] == \\'system\\' %}\\n        {{- messages[0][\\'content\\'] }}\\n    {%- else %}\\n        {{- \\'You are Qwen, created by Alibaba Cloud. You are a helpful assistant.\\' }}\\n    {%- endif %}\\n    {{- \"\\\\n\\\\n# Tools\\\\n\\\\nYou may call one or more functions to assist with the user query.\\\\n\\\\nYou are provided with function signatures within <tools></tools> XML tags:\\\\n<tools>\" }}\\n    {%- for tool in tools %}\\n        {{- \"\\\\n\" }}\\n        {{- tool | tojson }}\\n    {%- endfor %}\\n    {{- \"\\\\n</tools>\\\\n\\\\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\\\\n<tool_call>\\\\n{{\\\\\"name\\\\\": <function-name>, \\\\\"arguments\\\\\": <args-json-object>}}\\\\n</tool_call><|im_end|>\\\\n\" }}\\n{%- else %}\\n    {%- if messages[0][\\'role\\'] == \\'system\\' %}\\n        {{- \\'<|im_start|>system\\\\n\\' + messages[0][\\'content\\'] + \\'<|im_end|>\\\\n\\' }}\\n    {%- else %}\\n        {{- \\'<|im_start|>system\\\\nYou are Qwen, created by Alibaba Cloud. You are a helpful assistant.<|im_end|>\\\\n\\' }}\\n    {%- endif %}\\n{%- endif %}\\n{%- for message in messages %}\\n    {%- if (message.role == \"user\") or (message.role == \"system\" and not loop.first) or (message.role == \"assistant\" and not message.tool_calls) %}\\n        {{- \\'<|im_start|>\\' + message.role + \\'\\\\n\\' + message.content + \\'<|im_end|>\\' + \\'\\\\n\\' }}\\n    {%- elif message.role == \"assistant\" %}\\n        {{- \\'<|im_start|>\\' + message.role }}\\n        {%- if message.content %}\\n            {{- \\'\\\\n\\' + message.content }}\\n        {%- endif %}\\n        {%- for tool_call in message.tool_calls %}\\n            {%- if tool_call.function is defined %}\\n                {%- set tool_call = tool_call.function %}\\n            {%- endif %}\\n            {{- \\'\\\\n<tool_call>\\\\n{\"name\": \"\\' }}\\n            {{- tool_call.name }}\\n            {{- \\'\", \"arguments\": \\' }}\\n            {{- tool_call.arguments | tojson }}\\n            {{- \\'}\\\\n</tool_call>\\' }}\\n        {%- endfor %}\\n        {{- \\'<|im_end|>\\\\n\\' }}\\n    {%- elif message.role == \"tool\" %}\\n        {%- if (loop.index0 == 0) or (messages[loop.index0 - 1].role != \"tool\") %}\\n            {{- \\'<|im_start|>user\\' }}\\n        {%- endif %}\\n        {{- \\'\\\\n<tool_response>\\\\n\\' }}\\n        {{- message.content }}\\n        {{- \\'\\\\n</tool_response>\\' }}\\n        {%- if loop.last or (messages[loop.index0 + 1].role != \"tool\") %}\\n            {{- \\'<|im_end|>\\\\n\\' }}\\n        {%- endif %}\\n    {%- endif %}\\n{%- endfor %}\\n{%- if add_generation_prompt %}\\n    {{- \\'<|im_start|>assistant\\\\n\\' }}\\n{%- endif %}\\n', 'qwen2.attention.head_count_kv': '2', 'general.quantization_version': '2', 'tokenizer.ggml.model': 'gpt2', 'qwen2.feed_forward_length': '4864', 'qwen2.attention.head_count': '14'}\n", "Available chat formats from metadata: chat_template.default\n", "Using gguf chat template: {%- if tools %}\n", "    {{- '<|im_start|>system\\n' }}\n", "    {%- if messages[0]['role'] == 'system' %}\n", "        {{- messages[0]['content'] }}\n", "    {%- else %}\n", "        {{- 'You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.' }}\n", "    {%- endif %}\n", "    {{- \"\\n\\n# Tools\\n\\nYou may call one or more functions to assist with the user query.\\n\\nYou are provided with function signatures within <tools></tools> XML tags:\\n<tools>\" }}\n", "    {%- for tool in tools %}\n", "        {{- \"\\n\" }}\n", "        {{- tool | tojson }}\n", "    {%- endfor %}\n", "    {{- \"\\n</tools>\\n\\nFor each function call, return a json object with function name and arguments within <tool_call></tool_call> XML tags:\\n<tool_call>\\n{{\\\"name\\\": <function-name>, \\\"arguments\\\": <args-json-object>}}\\n</tool_call><|im_end|>\\n\" }}\n", "{%- else %}\n", "    {%- if messages[0]['role'] == 'system' %}\n", "        {{- '<|im_start|>system\\n' + messages[0]['content'] + '<|im_end|>\\n' }}\n", "    {%- else %}\n", "        {{- '<|im_start|>system\\nYou are <PERSON><PERSON>, created by Alibaba Cloud. You are a helpful assistant.<|im_end|>\\n' }}\n", "    {%- endif %}\n", "{%- endif %}\n", "{%- for message in messages %}\n", "    {%- if (message.role == \"user\") or (message.role == \"system\" and not loop.first) or (message.role == \"assistant\" and not message.tool_calls) %}\n", "        {{- '<|im_start|>' + message.role + '\\n' + message.content + '<|im_end|>' + '\\n' }}\n", "    {%- elif message.role == \"assistant\" %}\n", "        {{- '<|im_start|>' + message.role }}\n", "        {%- if message.content %}\n", "            {{- '\\n' + message.content }}\n", "        {%- endif %}\n", "        {%- for tool_call in message.tool_calls %}\n", "            {%- if tool_call.function is defined %}\n", "                {%- set tool_call = tool_call.function %}\n", "            {%- endif %}\n", "            {{- '\\n<tool_call>\\n{\"name\": \"' }}\n", "            {{- tool_call.name }}\n", "            {{- '\", \"arguments\": ' }}\n", "            {{- tool_call.arguments | tojson }}\n", "            {{- '}\\n</tool_call>' }}\n", "        {%- endfor %}\n", "        {{- '<|im_end|>\\n' }}\n", "    {%- elif message.role == \"tool\" %}\n", "        {%- if (loop.index0 == 0) or (messages[loop.index0 - 1].role != \"tool\") %}\n", "            {{- '<|im_start|>user' }}\n", "        {%- endif %}\n", "        {{- '\\n<tool_response>\\n' }}\n", "        {{- message.content }}\n", "        {{- '\\n</tool_response>' }}\n", "        {%- if loop.last or (messages[loop.index0 + 1].role != \"tool\") %}\n", "            {{- '<|im_end|>\\n' }}\n", "        {%- endif %}\n", "    {%- endif %}\n", "{%- endfor %}\n", "{%- if add_generation_prompt %}\n", "    {{- '<|im_start|>assistant\\n' }}\n", "{%- endif %}\n", "\n", "Using chat eos_token: <|im_end|>\n", "Using chat bos_token: <|endoftext|>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ LLM 就绪\n"]}], "source": ["from llama_cpp import Llama, LlamaGrammar\n", "\n", "# 模型路径（按你的实际环境改）\n", "MODEL_PATH = \"/home/<USER>/LLM_Models/Qwen2.5-0.5B-Instruct-GGUF/qwen2.5-0.5b-instruct-fp16.gguf\"\n", "\n", "llm = Llama(\n", "    model_path=MODEL_PATH,\n", "    n_threads=8,\n", "    n_ctx=2048\n", ")\n", "\n", "print(\"✅ LLM 就绪\")\n"]}, {"cell_type": "code", "execution_count": 26, "id": "a6b87c11", "metadata": {}, "outputs": [], "source": ["# 只约束“必须是一个 JSON 对象”\n", "GBNF_JSON = r'''\n", "ws ::= [ \\t\\n\\r]*\n", "root ::= ws object ws\n", "object ::= \"{\" ws pair (ws \",\" ws pair)* ws \"}\"\n", "pair ::= string ws \":\" ws value\n", "string ::= \"\\\"\" [^\\\"]* \"\\\"\"\n", "value ::= string | number | object | array | \"true\" | \"false\" | \"null\"\n", "array ::= \"[\" ws (value (ws \",\" ws value)*)? ws \"]\"\n", "number ::= \"-\"? [0-9]+ (\".\" [0-9]+)?\n", "'''\n", "grammar_json = LlamaGrammar.from_string(GBNF_JSON)\n"]}, {"cell_type": "code", "execution_count": 27, "id": "af086424", "metadata": {}, "outputs": [], "source": ["def format_chat_qwen(messages):\n", "    parts = []\n", "    for m in messages:\n", "        if m[\"role\"] == \"system\":\n", "            parts.append(f\"<|im_start|>system\\n{m['content']}<|im_end|>\\n\")\n", "        elif m[\"role\"] == \"user\":\n", "            parts.append(f\"<|im_start|>user\\n{m['content']}<|im_end|>\\n\")\n", "        elif m[\"role\"] == \"assistant\":\n", "            parts.append(f\"<|im_start|>assistant\\n{m['content']}<|im_end|>\\n\")\n", "    parts.append(\"<|im_start|>assistant\\n\")\n", "    return \"\".join(parts)\n", "\n", "def build_entity_hints(ha_index: dict) -> str:\n", "    lines = []\n", "    # 列出 by_name 里的“自然语言 → entity_id”映射，帮助模型选对实体\n", "    for alias, eids in ha_index.get(\"by_name\", {}).items():\n", "        eids_s = \", \".join(eids)\n", "        lines.append(f\"- {alias} => {eids_s}\")\n", "    # 再列出关键实体清单\n", "    lines.append(\"\\n实体清单：\")\n", "    for eid, meta in ha_index.get(\"by_entity\", {}).items():\n", "        fn = meta.get(\"friendly_name\",\"\")\n", "        lines.append(f\"- {fn} ({eid}) 支持服务: {', '.join(meta.get('services', []))}\")\n", "    return \"\\n\".join(lines)\n", "\n", "SYSTEM_BASE = (\n", "    \"你是 Home Assistant 控制器，必须只输出一个 JSON 对象：\"\n", "    '{\"domain\":\"...\",\"service\":\"...\",\"entity_id\":\"...\",\"args\":{...}}。\\n'\n", "    \"规则：\\n\"\n", "    \"1) 不要输出多余文字/Markdown。\\n\"\n", "    \"2) entity_id 必须来自可用实体提示；如果是灯，亮度用 brightness_pct(0-100)。\\n\"\n", "    \"3) 常见口语：\\n\"\n", "    \"   - 灯 light: turn_on/turn_off；调亮度=turn_on+brightness_pct。\\n\"\n", "    \"   - 风扇 fan: turn_on/turn_off/set_percentage。\\n\"\n", "    \"   - 窗帘 cover: open_cover/close_cover/stop_cover。\\n\"\n", "    \"   - 门锁 lock: lock/unlock。\\n\"\n", "    \"   - 加湿器 humidifier: turn_on/turn_off/set_humidity。\\n\"\n", "    \"   - 扫地机 vacuum: start/pause/stop/return_to_base/set_fan_speed。\\n\"\n", ")\n", "\n", "ENTITY_HINTS = build_entity_hints(HA_INDEX)\n"]}, {"cell_type": "code", "execution_count": 28, "id": "1bfbd4b5", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "def llm_parse_to_json(user_text: str, temperature: float = 0.1, max_tokens: int = 128) -> dict:\n", "    prompt = format_chat_qwen([\n", "        {\"role\":\"system\",\"content\": SYSTEM_BASE + \"\\n可用实体提示：\\n\" + ENTITY_HINTS},\n", "        {\"role\":\"user\",\"content\": user_text}\n", "    ])\n", "    out = llm.create_completion(\n", "        prompt=prompt,\n", "        max_tokens=max_tokens,\n", "        temperature=temperature,\n", "        grammar=grammar_json,     # 只保证“是 JSON”\n", "        stop=[\"<|im_end|>\"]\n", "    )\n", "    raw = out[\"choices\"][0][\"text\"].strip()\n", "    print(\"LLM RAW:\", raw)\n", "    try:\n", "        return json.loads(raw)\n", "    except Exception as e:\n", "        raise SystemExit(f\"模型输出不是合法 JSON：{e}\\nRAW:\\n{raw}\")\n", "\n", "def execute_nl(user_text: str, check_state: bool = True):\n", "    \"\"\"自然语言 → LLM(JSON) → normalize_cmd → run_cmd_safe\"\"\"\n", "    cmd_raw = llm_parse_to_json(user_text)\n", "    fixed, resp = run_cmd_safe(cmd_raw, check_state=check_state)\n", "    return fixed, resp\n"]}, {"cell_type": "code", "execution_count": 29, "id": "40c3ef53", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==============================\n", "指令: 把卧室灯开到 70%\n"]}, {"name": "stderr", "output_type": "stream", "text": ["llama_perf_context_print:        load time =    4258.36 ms\n", "llama_perf_context_print: prompt eval time =    4257.96 ms /   666 tokens (    6.39 ms per token,   156.41 tokens per second)\n", "llama_perf_context_print:        eval time =    2523.37 ms /    43 runs   (   58.68 ms per token,    17.04 tokens per second)\n", "llama_perf_context_print:       total time =    7356.16 ms /   709 tokens\n", "llama_perf_context_print:    graphs reused =         40\n", "Llama.generate: 653 prefix-match hit, remaining 13 prompt tokens to eval\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLM RAW: {\n", "  \"domain\": \"Home Assistant\",\n", "  \"service\": \"set_percentage\",\n", "  \"entity_id\": \"light.bed_light\",\n", "  \"args\": {\n", "    \"percentage\": 70\n", "  }\n", "}\n", "[ERROR] 服务不被允许: light.set_percentage for light.bed_light; 允许: ['turn_off', 'turn_on']\n", "\n", "==============================\n", "指令: 把客厅风扇调到 50%\n"]}, {"name": "stderr", "output_type": "stream", "text": ["llama_perf_context_print:        load time =    4258.36 ms\n", "llama_perf_context_print: prompt eval time =     794.41 ms /    13 tokens (   61.11 ms per token,    16.36 tokens per second)\n", "llama_perf_context_print:        eval time =    2885.16 ms /    46 runs   (   62.72 ms per token,    15.94 tokens per second)\n", "llama_perf_context_print:       total time =    4322.54 ms /    59 tokens\n", "llama_perf_context_print:    graphs reused =         43\n", "Llama.generate: 652 prefix-match hit, remaining 8 prompt tokens to eval\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLM RAW: {\n", "  \"domain\": \"Home Assistant\",\n", "  \"service\": \"set_percentage\",\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"args\": {\n", "    \"percentage\": 50\n", "  }\n", "}\n", "=== 规范化指令 ===\n", "{\n", "  \"domain\": \"fan\",\n", "  \"service\": \"set_percentage\",\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"args\": {\n", "    \"percentage\": 50\n", "  }\n", "}\n", "\n", "=== 等价 curl ===\n", "curl -X POST 'http://**************:8123/api/services/fan/set_percentage' -H 'Content-Type: application/json' -H 'Authorization: Bearer $HA_TOKEN' -d '{\"entity_id\": \"fan.living_room_fan\", \"percentage\": 50}'\n", "\n", "=== 返回 ===\n", "[\n", "  {\n", "    \"entity_id\": \"fan.living_room_fan\",\n", "    \"state\": \"on\",\n", "    \"attributes\": {\n", "      \"preset_modes\": [\n", "        \"auto\",\n", "        \"smart\",\n", "        \"sleep\",\n", "        \"on\"\n", "      ],\n", "      \"direction\": \"forward\",\n", "      \"oscillating\": false,\n", "      \"percentage\": 50,\n", "      \"percentage_step\": 33.333333333333336,\n", "      \"preset_mode\": null,\n", "      \"friendly_name\": \"Living Room Fan\",\n", "      \"supported_features\": 55\n", "    },\n", "    \"last_changed\": \"2025-09-16T08:02:21.114296+00:00\",\n", "    \"last_reported\": \"2025-09-16T08:02:21.114296+00:00\",\n", "    \"last_updated\": \"2025-09-16T08:02:21.114296+00:00\",\n", "    \"context\": {\n", "      \"id\": \"01K58RQKSSNHKA09CK3YWBM8H9\",\n", "      \"parent_id\": null,\n", "      \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "    }\n", "  }\n", "]\n", "\n", "=== 最新状态 ===\n", "{\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"state\": \"on\",\n", "  \"attributes\": {\n", "    \"preset_modes\": [\n", "      \"auto\",\n", "      \"smart\",\n", "      \"sleep\",\n", "      \"on\"\n", "    ],\n", "    \"direction\": \"forward\",\n", "    \"oscillating\": false,\n", "    \"percentage\": 50,\n", "    \"percentage_step\": 33.333333333333336,\n", "    \"preset_mode\": null,\n", "    \"friendly_name\": \"Living Room Fan\",\n", "    \"supported_features\": 55\n", "  },\n", "  \"last_changed\": \"2025-09-16T08:02:21.114296+00:00\",\n", "  \"last_reported\": \"2025-09-16T08:02:21.114296+00:00\",\n", "  \"last_updated\": \"2025-09-16T08:02:21.114296+00:00\",\n", "  \"context\": {\n", "    \"id\": \"01K58RQKSSNHKA09CK3YWBM8H9\",\n", "    \"parent_id\": null,\n", "    \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "  }\n", "}\n", "\n", "==============================\n", "指令: 打开厨房窗户\n"]}, {"name": "stderr", "output_type": "stream", "text": ["llama_perf_context_print:        load time =    4258.36 ms\n", "llama_perf_context_print: prompt eval time =     601.58 ms /     8 tokens (   75.20 ms per token,    13.30 tokens per second)\n", "llama_perf_context_print:        eval time =    2401.50 ms /    44 runs   (   54.58 ms per token,    18.32 tokens per second)\n", "llama_perf_context_print:       total time =    3558.30 ms /    52 tokens\n", "llama_perf_context_print:    graphs reused =         42\n", "Llama.generate: 652 prefix-match hit, remaining 8 prompt tokens to eval\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLM RAW: {\n", "  \"domain\": \"Home Assistant\",\n", "  \"service\": \"open_cover\",\n", "  \"entity_id\": \"cover.living_room_window\",\n", "  \"args\": {\n", "    \"state\": \"closed\"\n", "  }\n", "}\n", "=== 规范化指令 ===\n", "{\n", "  \"domain\": \"cover\",\n", "  \"service\": \"open_cover\",\n", "  \"entity_id\": \"cover.living_room_window\",\n", "  \"args\": {}\n", "}\n", "\n", "=== 等价 curl ===\n", "curl -X POST 'http://**************:8123/api/services/cover/open_cover' -H 'Content-Type: application/json' -H 'Authorization: Bearer $HA_TOKEN' -d '{\"entity_id\": \"cover.living_room_window\"}'\n", "\n", "=== 返回 ===\n", "[\n", "  {\n", "    \"entity_id\": \"cover.living_room_window\",\n", "    \"state\": \"opening\",\n", "    \"attributes\": {\n", "      \"current_position\": 0,\n", "      \"current_tilt_position\": 50,\n", "      \"friendly_name\": \"Living Room Window\",\n", "      \"supported_features\": 255\n", "    },\n", "    \"last_changed\": \"2025-09-16T08:02:25.012595+00:00\",\n", "    \"last_reported\": \"2025-09-16T08:02:25.012595+00:00\",\n", "    \"last_updated\": \"2025-09-16T08:02:25.012595+00:00\",\n", "    \"context\": {\n", "      \"id\": \"01K58RQQKM7QJMR4P9TF9S0HZP\",\n", "      \"parent_id\": null,\n", "      \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "    }\n", "  }\n", "]\n", "\n", "=== 最新状态 ===\n", "{\n", "  \"entity_id\": \"cover.living_room_window\",\n", "  \"state\": \"opening\",\n", "  \"attributes\": {\n", "    \"current_position\": 0,\n", "    \"current_tilt_position\": 50,\n", "    \"friendly_name\": \"Living Room Window\",\n", "    \"supported_features\": 255\n", "  },\n", "  \"last_changed\": \"2025-09-16T08:02:25.012595+00:00\",\n", "  \"last_reported\": \"2025-09-16T08:02:25.012595+00:00\",\n", "  \"last_updated\": \"2025-09-16T08:02:25.012595+00:00\",\n", "  \"context\": {\n", "    \"id\": \"01K58RQQKM7QJMR4P9TF9S0HZP\",\n", "    \"parent_id\": null,\n", "    \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "  }\n", "}\n", "\n", "==============================\n", "指令: 锁上前门\n"]}, {"name": "stderr", "output_type": "stream", "text": ["llama_perf_context_print:        load time =    4258.36 ms\n", "llama_perf_context_print: prompt eval time =     466.90 ms /     8 tokens (   58.36 ms per token,    17.13 tokens per second)\n", "llama_perf_context_print:        eval time =    2375.92 ms /    42 runs   (   56.57 ms per token,    17.68 tokens per second)\n", "llama_perf_context_print:       total time =    3395.52 ms /    50 tokens\n", "llama_perf_context_print:    graphs reused =         40\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLM RAW: {\n", "  \"domain\": \"Home Assistant\",\n", "  \"service\": \"lock\",\n", "  \"entity_id\": \"lock.front_door\",\n", "  \"args\": {\n", "    \"lock_state\": \"open\"\n", "  }\n", "}\n", "=== 规范化指令 ===\n", "{\n", "  \"domain\": \"lock\",\n", "  \"service\": \"lock\",\n", "  \"entity_id\": \"lock.front_door\",\n", "  \"args\": {}\n", "}\n", "\n", "=== 等价 curl ===\n", "curl -X POST 'http://**************:8123/api/services/lock/lock' -H 'Content-Type: application/json' -H 'Authorization: Bearer $HA_TOKEN' -d '{\"entity_id\": \"lock.front_door\"}'\n", "\n", "=== 返回 ===\n", "[\n", "  {\n", "    \"entity_id\": \"lock.front_door\",\n", "    \"state\": \"locking\",\n", "    \"attributes\": {\n", "      \"friendly_name\": \"Front Door\",\n", "      \"supported_features\": 0\n", "    },\n", "    \"last_changed\": \"2025-09-16T08:02:28.732125+00:00\",\n", "    \"last_reported\": \"2025-09-16T08:02:28.732125+00:00\",\n", "    \"last_updated\": \"2025-09-16T08:02:28.732125+00:00\",\n", "    \"context\": {\n", "      \"id\": \"01K58RQV7VWA1KP4C9FGMP05G1\",\n", "      \"parent_id\": null,\n", "      \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "    }\n", "  },\n", "  {\n", "    \"entity_id\": \"lock.front_door\",\n", "    \"state\": \"locked\",\n", "    \"attributes\": {\n", "      \"friendly_name\": \"Front Door\",\n", "      \"supported_features\": 0\n", "    },\n", "    \"last_changed\": \"2025-09-16T08:02:30.733291+00:00\",\n", "    \"last_reported\": \"2025-09-16T08:02:30.733291+00:00\",\n", "    \"last_updated\": \"2025-09-16T08:02:30.733291+00:00\",\n", "    \"context\": {\n", "      \"id\": \"01K58RQV7VWA1KP4C9FGMP05G1\",\n", "      \"parent_id\": null,\n", "      \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "    }\n", "  }\n", "]\n", "\n", "=== 最新状态 ===\n", "{\n", "  \"entity_id\": \"lock.front_door\",\n", "  \"state\": \"locked\",\n", "  \"attributes\": {\n", "    \"friendly_name\": \"Front Door\",\n", "    \"supported_features\": 0\n", "  },\n", "  \"last_changed\": \"2025-09-16T08:02:30.733291+00:00\",\n", "  \"last_reported\": \"2025-09-16T08:02:30.733291+00:00\",\n", "  \"last_updated\": \"2025-09-16T08:02:30.733291+00:00\",\n", "  \"context\": {\n", "    \"id\": \"01K58RQV7VWA1KP4C9FGMP05G1\",\n", "    \"parent_id\": null,\n", "    \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "  }\n", "}\n", "\n", "==============================\n", "指令: 加湿器设为 55%\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Llama.generate: 652 prefix-match hit, remaining 14 prompt tokens to eval\n", "llama_perf_context_print:        load time =    4258.36 ms\n", "llama_perf_context_print: prompt eval time =     688.46 ms /    14 tokens (   49.18 ms per token,    20.34 tokens per second)\n", "llama_perf_context_print:        eval time =    1517.46 ms /    25 runs   (   60.70 ms per token,    16.47 tokens per second)\n", "llama_perf_context_print:       total time =    2571.01 ms /    39 tokens\n", "llama_perf_context_print:    graphs reused =         23\n", "Llama.generate: 652 prefix-match hit, remaining 17 prompt tokens to eval\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLM RAW: {\"domain\":\"...\",\"service\":\"set_humidity\",\"entity_id\":\"...\",\"args\":{\"humidity_pct\":55}}\n", "[ERROR] 实体不存在或不在白名单: ...\n", "\n", "==============================\n", "指令: 扫地机调到 high 风速并开始\n"]}, {"name": "stderr", "output_type": "stream", "text": ["llama_perf_context_print:        load time =    4258.36 ms\n", "llama_perf_context_print: prompt eval time =    1030.61 ms /    17 tokens (   60.62 ms per token,    16.50 tokens per second)\n", "llama_perf_context_print:        eval time =    3429.16 ms /    48 runs   (   71.44 ms per token,    14.00 tokens per second)\n", "llama_perf_context_print:       total time =    5147.13 ms /    65 tokens\n", "llama_perf_context_print:    graphs reused =         45\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLM RAW: {\n", "  \"domain\": \"Home Assistant\",\n", "  \"service\": \"set_fan_speed\",\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"args\": {\n", "    \"fan_speed\": \"high\"\n", "  }\n", "}\n", "=== 规范化指令 ===\n", "{\n", "  \"domain\": \"fan\",\n", "  \"service\": \"set_percentage\",\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"args\": {\n", "    \"percentage\": 0\n", "  }\n", "}\n", "\n", "=== 等价 curl ===\n", "curl -X POST 'http://**************:8123/api/services/fan/set_percentage' -H 'Content-Type: application/json' -H 'Authorization: Bearer $HA_TOKEN' -d '{\"entity_id\": \"fan.living_room_fan\", \"percentage\": 0}'\n", "\n", "=== 返回 ===\n", "[\n", "  {\n", "    \"entity_id\": \"fan.living_room_fan\",\n", "    \"state\": \"off\",\n", "    \"attributes\": {\n", "      \"preset_modes\": [\n", "        \"auto\",\n", "        \"smart\",\n", "        \"sleep\",\n", "        \"on\"\n", "      ],\n", "      \"direction\": \"forward\",\n", "      \"oscillating\": false,\n", "      \"percentage\": 0,\n", "      \"percentage_step\": 33.333333333333336,\n", "      \"preset_mode\": null,\n", "      \"friendly_name\": \"Living Room Fan\",\n", "      \"supported_features\": 55\n", "    },\n", "    \"last_changed\": \"2025-09-16T08:02:39.128125+00:00\",\n", "    \"last_reported\": \"2025-09-16T08:02:39.128125+00:00\",\n", "    \"last_updated\": \"2025-09-16T08:02:39.128125+00:00\",\n", "    \"context\": {\n", "      \"id\": \"01K58RR5CQP9KE48DNCM64MYSX\",\n", "      \"parent_id\": null,\n", "      \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "    }\n", "  }\n", "]\n", "\n", "=== 最新状态 ===\n", "{\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"state\": \"off\",\n", "  \"attributes\": {\n", "    \"preset_modes\": [\n", "      \"auto\",\n", "      \"smart\",\n", "      \"sleep\",\n", "      \"on\"\n", "    ],\n", "    \"direction\": \"forward\",\n", "    \"oscillating\": false,\n", "    \"percentage\": 0,\n", "    \"percentage_step\": 33.333333333333336,\n", "    \"preset_mode\": null,\n", "    \"friendly_name\": \"Living Room Fan\",\n", "    \"supported_features\": 55\n", "  },\n", "  \"last_changed\": \"2025-09-16T08:02:39.128125+00:00\",\n", "  \"last_reported\": \"2025-09-16T08:02:39.128125+00:00\",\n", "  \"last_updated\": \"2025-09-16T08:02:39.128125+00:00\",\n", "  \"context\": {\n", "    \"id\": \"01K58RR5CQP9KE48DNCM64MYSX\",\n", "    \"parent_id\": null,\n", "    \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "  }\n", "}\n"]}], "source": ["tests = [\n", "    \"把卧室灯开到 70%\",\n", "    \"把客厅风扇调到 50%\",\n", "    \"打开厨房窗户\",\n", "    \"锁上前门\",\n", "    \"加湿器设为 55%\",\n", "    \"扫地机调到 high 风速并开始\"\n", "]\n", "\n", "for t in tests:\n", "    print(\"\\n==============================\")\n", "    print(\"指令:\", t)\n", "    try:\n", "        execute_nl(t, check_state=True)\n", "    except SystemExit as e:\n", "        print(\"[ERROR]\", e)\n"]}], "metadata": {"kernelspec": {"display_name": "llamacpp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}