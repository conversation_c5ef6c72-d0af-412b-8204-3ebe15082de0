{"cells": [{"cell_type": "code", "execution_count": null, "id": "a3d4b86e", "metadata": {}, "outputs": [], "source": ["# ==== 基本配置（按需修改）====\n", "import os, json, requests, textwrap, re\n", "from datetime import datetime\n", "from llama_cpp import Llama, LlamaGrammar\n", "\n", "HA_URL   = os.getenv(\"HA_URL\",   \"http://**************:8123\").rstrip(\"/\")\n", "HA_TOKEN = os.getenv(\"HA_TOKEN\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI2ZmI5MTgwMjQzYWM0MmVlYTAyMzMwZWM0YWZmYzEzYyIsImlhdCI6MTc1ODAwMzY0NywiZXhwIjoyMDczMzYzNjQ3fQ.RGPLPOkRo03xAaRgNF5dD_2KDuBa0UO9aaSbjp0X6DA\")  # 也可以 export HA_TOKEN=... 后不改这里\n", "\n", "TIMEOUT  = 15\n", "\n", "def _headers_json():\n", "    return {\"Authorization\": f\"Bearer {HA_TOKEN}\", \"Content-Type\": \"application/json\"}\n", "\n", "def _headers_auth():\n", "    return {\"Authorization\": f\"Bearer {HA_TOKEN}\"}\n", "\n", "def jprint(obj):\n", "    print(json.dumps(obj, ensure_ascii=False, indent=2))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "bc52aba5", "metadata": {}, "outputs": [], "source": ["def call_service(domain: str, service: str, entity_id: str, **kwargs):\n", "    url  = f\"{HA_URL}/api/services/{domain}/{service}\"\n", "    body = {\"entity_id\": entity_id}; body.update(kwargs or {})\n", "    r = requests.post(url, headers=_headers_json(), json=body, timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    try:\n", "        return r.json()\n", "    except ValueError:\n", "        return {\"ok\": True, \"status_code\": r.status_code, \"text\": r.text}\n", "\n", "def get_state(entity_id: str):\n", "    url = f\"{HA_URL}/api/states/{entity_id}\"\n", "    r = requests.get(url, headers=_headers_auth(), timeout=TIMEOUT)\n", "    r.raise_for_status()\n", "    return r.json()\n"]}, {"cell_type": "code", "execution_count": null, "id": "95dffb97", "metadata": {}, "outputs": [], "source": ["HA_INDEX = {\n", "  \"by_entity\": {\n", "    \"humidifier.humidifier\": {\n", "      \"friendly_name\": \"Humidifier\",\n", "      \"domain\": \"humidifier\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_humidity\"],\n", "      \"args\": { \"set_humidity\": [\"humidity\"] }\n", "    },\n", "    \"light.bed_light\": {\n", "      \"friendly_name\": \"Bed Light\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\", \"color_temp_kelvin\"] }\n", "    },\n", "    \"light.ceiling_lights\": {\n", "      \"friendly_name\": \"Ceiling Lights\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\"] }\n", "    },\n", "    \"light.kitchen_lights\": {\n", "      \"friendly_name\": \"Kitchen Lights\",\n", "      \"domain\": \"light\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"brightness_pct\"] }\n", "    },\n", "    \"fan.ceiling_fan\": {\n", "      \"friendly_name\": \"Ceiling Fan\",\n", "      \"domain\": \"fan\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_percentage\"],\n", "      \"args\": { \"set_percentage\": [\"percentage\"] }\n", "    },\n", "    \"fan.living_room_fan\": {\n", "      \"friendly_name\": \"Living Room Fan\",\n", "      \"domain\": \"fan\",\n", "      \"services\": [\"turn_on\", \"turn_off\", \"set_percentage\"],\n", "      \"args\": { \"set_percentage\": [\"percentage\"] }\n", "    },\n", "    \"vacuum.demo_vacuum_0_ground_floor\": {\n", "      \"friendly_name\": \"Demo vacuum 0 ground floor\",\n", "      \"domain\": \"vacuum\",\n", "      \"services\": [\"start\", \"pause\", \"stop\", \"return_to_base\", \"set_fan_speed\"],\n", "      \"args\": { \"set_fan_speed\": [\"fan_speed\"] }\n", "    },\n", "    \"lock.front_door\": {\n", "      \"friendly_name\": \"Front Door\",\n", "      \"domain\": \"lock\",\n", "      \"services\": [\"lock\", \"unlock\"]\n", "    },\n", "    \"lock.kitchen_door\": {\n", "      \"friendly_name\": \"Kitchen Door\",\n", "      \"domain\": \"lock\",\n", "      \"services\": [\"lock\", \"unlock\"]\n", "    },\n", "    # \"sensor.outside_temperature\": {\n", "    #   \"friendly_name\": \"Outside Temperature\",\n", "    #   \"domain\": \"sensor\",\n", "    #   \"services\": []\n", "    # },\n", "    # \"sensor.carbon_dioxide\": {\n", "    #   \"friendly_name\": \"Carbon Dioxide\",\n", "    #   \"domain\": \"sensor\",\n", "    #   \"services\": []\n", "    # },\n", "    \"cover.kitchen_window\": {\n", "      \"friendly_name\": \"Kitchen Window\",\n", "      \"domain\": \"cover\",\n", "      \"services\": [\"open_cover\", \"close_cover\", \"stop_cover\"]\n", "    },\n", "    \"cover.living_room_window\": {\n", "      \"friendly_name\": \"Living Room Window\",\n", "      \"domain\": \"cover\",\n", "      \"services\": [\"open_cover\", \"close_cover\", \"stop_cover\"]\n", "    },\n", "    \"siren.siren\": {\n", "      \"friendly_name\": \"<PERSON><PERSON>\",\n", "      \"domain\": \"siren\",\n", "      \"services\": [\"turn_on\", \"turn_off\"],\n", "      \"args\": { \"turn_on\": [\"tone\", \"volume_level\"] }\n", "    }\n", "  },\n", "  \"by_name\": {\n", "    \"humidifier\": [\"humidifier.humidifier\"],\n", "    \"bed light\": [\"light.bed_light\"],\n", "    \"ceiling lights\": [\"light.ceiling_lights\"],\n", "    \"kitchen lights\": [\"light.kitchen_lights\"],\n", "    \"ceiling fan\": [\"fan.ceiling_fan\"],\n", "    \"living room fan\": [\"fan.living_room_fan\"],\n", "    \"vacuum\": [\"vacuum.demo_vacuum_0_ground_floor\"],\n", "    \"front door\": [\"lock.front_door\"],\n", "    \"kitchen door\": [\"lock.kitchen_door\"],\n", "    # \"outside temperature\": [\"sensor.outside_temperature\"],\n", "    # \"carbon dioxide\": [\"sensor.carbon_dioxide\"],\n", "    \"kitchen window\": [\"cover.kitchen_window\"],\n", "    \"living room window\": [\"cover.living_room_window\"],\n", "    \"siren\": [\"siren.siren\"]\n", "  }\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "id": "112c0313", "metadata": {}, "outputs": [], "source": ["BY_NAME_CN = {\n", "    \"卧室灯\": [\"light.bed_light\"],\n", "    \"天花板灯\": [\"light.ceiling_lights\"],\n", "    \"厨房灯\": [\"light.kitchen_lights\"],\n", "    \"客厅风扇\": [\"fan.living_room_fan\"],\n", "    \"吊扇\": [\"fan.ceiling_fan\"],\n", "    \"厨房窗户\": [\"cover.kitchen_window\"],\n", "    \"客厅窗户\": [\"cover.living_room_window\"],\n", "    \"前门\": [\"lock.front_door\"],\n", "    \"厨房门\": [\"lock.kitchen_door\"],\n", "    \"扫地机\": [\"vacuum.demo_vacuum_0_ground_floor\"],\n", "    \"加湿器\": [\"humidifier.humidifier\"],\n", "    \"警报器\": [\"siren.siren\"],\n", "}\n", "HA_INDEX[\"by_name\"].update(BY_NAME_CN)\n", "\n", "def build_entity_hints(ha_index: dict) -> str:\n", "    lines = []\n", "    for alias, eids in ha_index.get(\"by_name\", {}).items():\n", "        eids_s = \", \".join(eids)\n", "        lines.append(f\"- {alias} => {eids_s}\")\n", "    return \"\\n\".join(lines)\n", "\n", "print(build_entity_hints(HA_INDEX))\n"]}, {"cell_type": "code", "execution_count": null, "id": "1e4cf738", "metadata": {}, "outputs": [], "source": ["DOMAIN_MAP = {\"lights\": \"light\", \"fans\": \"fan\"}\n", "SERVICE_MAP = {\n", "  \"light\": {\"set_brightness\": \"turn_on\", \"turn_on\": \"turn_on\", \"turn_off\": \"turn_off\"},\n", "  \"fan\": {\"set_speed\": \"set_percentage\", \"set_percentage\": \"set_percentage\", \"turn_on\": \"turn_on\", \"turn_off\": \"turn_off\"},\n", "  \"cover\": {\"open\": \"open_cover\", \"close\": \"close_cover\", \"stop\": \"stop_cover\"},\n", "  \"lock\": {\"lock\": \"lock\", \"unlock\": \"unlock\"},\n", "  \"humidifier\": {\"set_humidity\": \"set_humidity\", \"turn_on\": \"turn_on\", \"turn_off\": \"turn_off\"},\n", "  \"vacuum\": {\"start\": \"start\", \"pause\": \"pause\", \"stop\": \"stop\", \"return\": \"return_to_base\", \"set_fan_speed\": \"set_fan_speed\"},\n", "  \"siren\": {\"turn_on\": \"turn_on\", \"turn_off\": \"turn_off\"}\n", "}\n", "\n", "def resolve_entity_id(text: str) -> str | None:\n", "    print(f\"🔍 resolve_entity_id: text={text}\")\n", "    text_norm = text.strip().lower()\n", "    print(f\"🔍 resolve_entity_id: text={text}\")\n", "\n", "    if \".\" in text_norm and text_norm in HA_INDEX[\"by_entity\"]:\n", "        print(f\"🔍 found in by_entity: {text_norm}\")\n", "        return text_norm\n", "    if text_norm in HA_INDEX[\"by_name\"]:\n", "        result = HA_INDEX[\"by_name\"][text_norm][0]\n", "        print(f\"🔍 found in by_name: {text_norm} -> {result}\")\n", "        return HA_INDEX[\"by_name\"][text_norm][0]\n", "    for eid, meta in HA_INDEX[\"by_entity\"].items():\n", "        if text_norm in meta.get(\"friendly_name\",\"\").lower():\n", "            print(f\"🔍 found in friendly_name: {text_norm} -> {eid}\")\n", "            return eid\n", "    return None\n", "\n", "def normalize_cmd(cmd: dict) -> dict:\n", "    eid = resolve_entity_id(cmd[\"entity_id\"])\n", "    if not eid:\n", "        raise SystemExit(f\"实体无法解析: {cmd['entity_id']}\")\n", "    domain = eid.split(\".\",1)[0]\n", "    service_std = SERVICE_MAP.get(domain, {}).get(cmd[\"service\"], cmd[\"service\"])\n", "    if service_std not in HA_INDEX[\"by_entity\"][eid][\"services\"]:\n", "        raise SystemExit(f\"服务不被允许: {domain}.{service_std}\")\n", "    args = cmd.get(\"args\", {})\n", "    return {\"domain\": domain, \"service\": service_std, \"entity_id\": eid, \"args\": args}\n", "\n", "def run_cmd_safe(cmd: dict, check_state=True):\n", "    fixed = normalize_cmd(cmd)\n", "    print(\"=== 指令 ===\")\n", "    jprint(fixed)\n", "    resp = call_service(fixed[\"domain\"], fixed[\"service\"], fixed[\"entity_id\"], **fixed[\"args\"])\n", "    print(\"=== 返回 ===\")\n", "    jprint(resp)\n", "    if check_state:\n", "        st = get_state(fixed[\"entity_id\"])\n", "        print(\"=== 最新状态 ===\")\n", "        jprint(st)\n", "    return fixed, resp\n"]}, {"cell_type": "code", "execution_count": null, "id": "01261021", "metadata": {}, "outputs": [], "source": ["# === 覆盖并增强：域名/服务别名映射 ===\n", "\n", "# 允许的真实域（用于 grammar 不收敛时的强约束参考）\n", "ALLOWED_DOMAINS = {\"light\",\"fan\",\"cover\",\"lock\",\"humidifier\",\"vacuum\",\"siren\",\"sensor\"}\n", "\n", "# 常见“口误域名”→ 真实域；空串表示“忽略它，用 entity_id 前缀判定”\n", "DOMAIN_MAP.update({\n", "    \"homeassistant\": \"\",\n", "    \"home\": \"\",\n", "    \"humidity\": \"humidifier\",     # 把“humidity”口误归到 humidifier\n", "    \"lights\": \"light\",\n", "    \"fans\": \"fan\",\n", "    \"sirens\": \"siren\",\n", "    \"covers\": \"cover\",\n", "    \"locks\": \"lock\",\n", "    \"vacuums\": \"vacuum\",\n", "})\n", "\n", "# 各域服务别名更激进地收敛到标准服务\n", "SERVICE_MAP.setdefault(\"light\", {}).update({\n", "    \"set_light\": \"turn_on\",\n", "    \"set\": \"turn_on\",\n", "})\n", "SERVICE_MAP.setdefault(\"fan\", {}).update({\n", "    \"set\": \"set_percentage\",\n", "})\n", "SERVICE_MAP.setdefault(\"cover\", {}).update({\n", "    \"open\": \"open_cover\",\n", "    \"close\": \"close_cover\",\n", "    \"stop\": \"stop_cover\",\n", "})\n", "SERVICE_MAP.setdefault(\"lock\", {}).update({\n", "    # 有时模型把实体拼进 service，比如 \"lock.front_door\"\n", "    \"lock.front_door\": \"lock\",\n", "    \"unlock.front_door\": \"unlock\",\n", "})\n", "SERVICE_MAP.setdefault(\"humidifier\", {}).update({\n", "    \"set\": \"set_humidity\",\n", "})\n", "SERVICE_MAP.setdefault(\"vacuum\", {}).update({\n", "    \"dock\": \"return_to_base\",\n", "    \"return\": \"return_to_base\",\n", "})\n", "\n", "# 将“通用 value”参数映射到各域的真实字段\n", "ARG_KEY_MAP = {\n", "    \"light.turn_on\": {\"value\": \"brightness_pct\", \"percentage\": \"brightness_pct\"},\n", "    \"fan.set_percentage\": {\"value\": \"percentage\"},\n", "    \"humidifier.set_humidity\": {\"value\": \"humidity\", \"humidity_pct\": \"humidity\"},\n", "    \"vacuum.set_fan_speed\": {\"speed\": \"fan_speed\", \"value\": \"fan_speed\"},\n", "}\n", "\n", "# def _remap_args(domain: str, service: str, args: dict) -> dict:\n", "#     args = dict(args or {})\n", "#     key_map = ARG_KEY_MAP.get(f\"{domain}.{service}\", {})\n", "#     for k_src, k_dst in list(key_map.items()):\n", "#         if k_src in args and k_dst not in args:\n", "#             args[k_dst] = args.pop(k_src)\n", "#     return args\n", "\n", "def _remap_args(domain: str, service: str, raw_args: dict) -> dict:\n", "    \"\"\"参数重映射\"\"\"\n", "    args = {}\n", "    \n", "    for k, v in raw_args.items():\n", "        # 通用映射\n", "        if k in (\"value\", \"level\", \"percentage_value\", \"percent\"):\n", "            if domain == \"light\" and service == \"turn_on\":\n", "                args[\"brightness_pct\"] = v\n", "            elif domain == \"fan\" and service == \"set_percentage\":\n", "                args[\"percentage\"] = v\n", "            elif domain == \"humidifier\" and service == \"set_humidity\":\n", "                args[\"humidity\"] = v\n", "            else:\n", "                args[k] = v\n", "        elif k in (\"humidity_pct\", \"humidity_percentage\"):\n", "            args[\"humidity\"] = v\n", "        elif k in (\"brightness_pct\", \"brightness_percentage\", \"brightness\"):\n", "            args[\"brightness_pct\"] = v\n", "        elif k in (\"fan_speed\", \"speed\"):\n", "            if domain == \"vacuum\":\n", "                args[\"fan_speed\"] = v\n", "            elif domain == \"fan\":\n", "                args[\"percentage\"] = v  # 风扇速度转换为百分比\n", "            else:\n", "                args[k] = v\n", "        else:\n", "            args[k] = v\n", "    \n", "    return args\n"]}, {"cell_type": "code", "execution_count": null, "id": "885d53a1", "metadata": {}, "outputs": [], "source": ["# === 增强：基于用户原句的“实体优先级”纠错（当 LLM 挑错实体时兜底）===\n", "\n", "# 关键字 → 建议实体 的启发式（根据你的 demo 面板）\n", "INTENT_ALIAS_HINTS = [\n", "    ((\"卧室灯\",\"bed\",\"卧室\"), \"light.bed_light\"),\n", "    ((\"天花板灯\",\"顶灯\",\"ceiling\"), \"light.ceiling_lights\"),\n", "    ((\"厨房灯\",\"厨房\"), \"light.kitchen_lights\"),\n", "    ((\"客厅风扇\",\"客厅风\"), \"fan.living_room_fan\"),\n", "    ((\"吊扇\"), \"fan.ceiling_fan\"),\n", "    ((\"厨房窗\",\"厨房窗户\",\"kitchen window\"), \"cover.kitchen_window\"),\n", "    ((\"客厅窗\",\"客厅窗户\",\"living room window\"), \"cover.living_room_window\"),\n", "    ((\"前门\",\"front door\"), \"lock.front_door\"),\n", "    ((\"厨房门\",\"kitchen door\"), \"lock.kitchen_door\"),\n", "    ((\"加湿器\",\"humidifier\"), \"humidifier.humidifier\"),\n", "    ((\"扫地机\",\"vacuum\"), \"vacuum.demo_vacuum_0_ground_floor\"),\n", "    ((\"警报器\",\"siren\",\"报警\"), \"siren.siren\"),\n", "]\n", "\n", "# ✅ 把这些追加到你已有的 INTENT_ALIAS_HINTS 之前（提高优先级）\n", "EXTRA_HINTS = [\n", "    ((\"厨房窗\", \"厨房的窗\", \"厨房窗户\", \"开厨房窗\", \"打开厨房窗\", \"打开厨房的窗\", \"打开厨房窗户\", \"kitchen window\"),\n", "     \"cover.kitchen_window\"),\n", "    ((\"客厅窗\", \"客厅的窗\", \"客厅窗户\", \"开客厅窗\", \"打开客厅窗\", \"打开客厅的窗\", \"打开客厅窗户\", \"living room window\"),\n", "     \"cover.living_room_window\"),\n", "]\n", "\n", "# 如果你之前已定义 INTENT_ALIAS_HINTS，这里做“前置合并”，避免重复\n", "try:\n", "    # 去重合并（保持 EXTRA_HINTS 在最前）\n", "    _existing = set((tuple(k if isinstance(k, tuple) else (k,)), v) for k, v in INTENT_ALIAS_HINTS)\n", "    _new = []\n", "    for k, v in EXTRA_HINTS:\n", "        key = (tuple(k if isinstance(k, tuple) else (k,)), v)\n", "        if key not in _existing:\n", "            _new.append((k, v))\n", "    INTENT_ALIAS_HINTS = _new + INTENT_ALIAS_HINTS\n", "except NameError:\n", "    # 还没定义 INTENT_ALIAS_HINTS 的情况\n", "    INTENT_ALIAS_HINTS = list(EXTRA_HINTS)\n", "\n", "\n", "\n", "\n", "def resolve_entity_id_smart(entity_text: str, user_text: str | None = None) -> str | None:\n", "    \"\"\"\n", "    先用你原来的 resolve_entity_id；若失败或领域/动作冲突，再用 user_text 里的关键词兜底。\n", "    \"\"\"\n", "    print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "    eid = resolve_entity_id(entity_text) if entity_text else None\n", "    print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "\n", "    if eid:\n", "        return eid\n", "    ut = (user_text or \"\").lower()\n", "\n", "    print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "    \n", "    for keywords, candidate in INTENT_ALIAS_HINTS:\n", "        if isinstance(keywords, str):\n", "            keywords = (keywords,)\n", "        if any(k.lower() in ut for k in keywords):\n", "            print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "            return candidate\n", "        \n", "    print(f\"🔍 resolve_entity_id_smart: entity_text={entity_text}, user_text={user_text}\")\n", "    return None\n", "\n", "def normalize_cmd_with_text(cmd: dict, user_text: str | None = None) -> dict:\n", "    \"\"\"\n", "    比 normalize_cmd 更强：\n", "      1) entity_id 优先从 LLM 输出解析，若不行用 user_text 关键词兜底；\n", "      2) domain 以 entity_id 前缀为准，若 LLM 给了“homeassistant”等则忽略；\n", "      3) service 用 SERVICE_MAP 收敛；若 service 不合法，结合域的典型操作做一次强纠；\n", "      4) 参数把 value/percentage/humidity_pct 等映射成各自真实字段，并做范围截断。\n", "    \"\"\"\n", "    raw_domain  = (cmd.get(\"domain\")  or \"\").strip().lower()\n", "    raw_service = (cmd.get(\"service\") or \"\").strip().lower()\n", "    raw_eid     = (cmd.get(\"entity_id\") or \"\").strip()\n", "    raw_args    = cmd.get(\"args\") or {}\n", "\n", "    print(f\"🔍 DEBUG: raw_domain={raw_domain}, raw_service={raw_service}, raw_eid={raw_eid}\")\n", "\n", "    # 1) 实体解析（允许别名/友好名/关键词兜底）\n", "    eid = resolve_entity_id_smart(raw_eid, user_text=user_text)\n", "    if not eid:\n", "        raise SystemExit(f\"实体无法解析: {raw_eid!r}\")\n", "\n", "    true_domain = eid.split(\".\", 1)[0]\n", "\n", "    # 2) 规范域：优先 entity_id 前缀，其次 DOMAIN_MAP\n", "    mapped = DOMAIN_MAP.get(raw_domain, raw_domain)\n", "    domain = true_domain if (mapped in (\"\", None) or mapped not in ALLOWED_DOMAINS) else mapped\n", "\n", "    # 3) 规范服务：基于最终确定的 domain 重新映射 service\n", "    service = SERVICE_MAP.get(domain, {}).get(raw_service, raw_service)\n", "\n", "    # 4) 先把参数做一次键名映射（value→brightness_pct 等）\n", "    args = _remap_args(domain, service, raw_args)\n", "\n", "    # 5) 白名单校验（服务允许性 & 允许参数）\n", "    meta = HA_INDEX[\"by_entity\"].get(eid)\n", "    if not meta:\n", "        raise SystemExit(f\"实体不在白名单: {eid}\")\n", "    allowed_svcs = set(meta.get(\"services\", []))\n", "    if service not in allowed_svcs:\n", "        # 按域做一次“强纠”：根据用户意图关键字修成最常见服务\n", "        # 灯：默认 turn_on；风扇：set_percentage（若出现数字/百分比）；窗帘：open/close；锁：lock/unlock；加湿器：set_humidity；扫地机：start\n", "        ut = (user_text or \"\").lower()\n", "        if domain == \"light\":\n", "            service = \"turn_on\"\n", "        elif domain == \"fan\":\n", "            service = \"set_percentage\" if any(x in (args.keys() | {\"value\",\"percentage\"}) for x in (\"percentage\",\"value\")) or re.search(r\"\\d+%?\", ut) else \"turn_on\"\n", "        elif domain == \"cover\":\n", "            service = \"open_cover\" if any(k in ut for k in (\"开\",\"open\")) else \"close_cover\" if any(k in ut for k in (\"关\",\"close\")) else \"stop_cover\"\n", "        elif domain == \"lock\":\n", "            service = \"lock\" if any(k in ut for k in (\"锁上\",\"上锁\",\"lock\")) else \"unlock\" if \"解锁\" in ut or \"unlock\" in ut else \"lock\"\n", "        elif domain == \"humidifier\":\n", "            service = \"set_humidity\" if any(k in ut for k in (\"%\",\"湿\")) or any(k in args for k in (\"humidity\",\"value\",\"humidity_pct\")) else \"turn_on\"\n", "        elif domain == \"vacuum\":\n", "            service = \"start\"\n", "        elif domain == \"siren\":\n", "            service = \"turn_on\"\n", "        # 再次校验\n", "        if service not in allowed_svcs:\n", "            raise SystemExit(f\"服务不被允许: {domain}.{service} for {eid}; 允许: {sorted(allowed_svcs)}\")\n", "\n", "    # 6) 只保留允许参数，并做数值范围归一\n", "    allowed_args = set(meta.get(\"args\", {}).get(service, []))\n", "    args = {k:v for k,v in args.items() if k in allowed_args}\n", "\n", "    # 数值归一\n", "    if domain == \"light\" and service == \"turn_on\" and \"brightness_pct\" in args:\n", "        try:\n", "            args[\"brightness_pct\"] = max(0, min(100, int(args[\"brightness_pct\"])))\n", "        except Exception:\n", "            args.pop(\"brightness_pct\", None)\n", "\n", "    if domain == \"fan\" and service == \"set_percentage\" and \"percentage\" in args:\n", "        try:\n", "            args[\"percentage\"] = max(0, min(100, int(args[\"percentage\"])))\n", "        except Exception:\n", "            raise SystemExit(\"fan.set_percentage 需要整数 percentage 0-100\")\n", "\n", "    if domain == \"humidifier\" and service == \"set_humidity\" and \"humidity\" in args:\n", "        try:\n", "            args[\"humidity\"] = max(0, min(100, int(args[\"humidity\"])))\n", "        except Exception:\n", "            raise SystemExit(\"humidifier.set_humidity 需要整数 humidity 0-100\")\n", "\n", "    return {\"domain\": domain, \"service\": service, \"entity_id\": eid, \"args\": args}\n", "\n", "    # 在函数最后，return 之前添加：\n", "    print(f\"🔍 FINAL: domain={domain}, service={service}, entity_id={eid}, args={args}\")\n", "    return {\"domain\": domain, \"service\": service, \"entity_id\": eid, \"args\": args}"]}, {"cell_type": "code", "execution_count": null, "id": "72933d2d", "metadata": {}, "outputs": [], "source": ["def normalize_cmd_with_text_v2(cmd: dict, user_text: str | None = None) -> dict:\n", "    \"\"\"\n", "    重构版本：更简单、更可靠的命令规范化\n", "    \"\"\"\n", "    raw_domain = (cmd.get(\"domain\") or \"\").strip().lower()\n", "    raw_service = (cmd.get(\"service\") or \"\").strip().lower()\n", "    raw_eid = (cmd.get(\"entity_id\") or \"\").strip()\n", "    raw_args = cmd.get(\"args\") or {}\n", "    \n", "    print(f\"🔍 输入: domain={raw_domain}, service={raw_service}, entity_id={raw_eid}\")\n", "    \n", "    # 1. 实体解析 - 简化逻辑，优先使用关键词匹配\n", "    eid = None\n", "    \n", "    # 1.1 先尝试直接匹配（如果是正确的entity_id）\n", "    if raw_eid and \".\" in raw_eid:\n", "        entity_key = raw_eid.lower()\n", "        if entity_key in HA_INDEX[\"by_entity\"]:\n", "            eid = entity_key\n", "            print(f\"🔍 直接匹配成功: {eid}\")\n", "    \n", "    # 1.2 如果直接匹配失败，使用关键词兜底\n", "    if not eid and user_text:\n", "        ut = user_text.lower()\n", "        print(f\"🔍 关键词匹配，用户输入: {ut}\")\n", "        \n", "        # 优先匹配中文关键词\n", "        for keywords, candidate in INTENT_ALIAS_HINTS:\n", "            if isinstance(keywords, str):\n", "                keywords = (keywords,)\n", "            for keyword in keywords:\n", "                if keyword.lower() in ut:\n", "                    eid = candidate\n", "                    print(f\"🔍 关键词匹配成功: '{keyword}' -> {eid}\")\n", "                    break\n", "            if eid:\n", "                break\n", "    \n", "    # 1.3 最后尝试by_name匹配\n", "    if not eid:\n", "        for name, candidates in HA_INDEX[\"by_name\"].items():\n", "            if name.lower() in (user_text or \"\").lower():\n", "                eid = candidates[0]\n", "                print(f\"🔍 by_name匹配成功: '{name}' -> {eid}\")\n", "                break\n", "    \n", "    if not eid:\n", "        raise SystemExit(f\"无法解析实体: {raw_eid}, 用户输入: {user_text}\")\n", "    \n", "    # 2. 确定正确的domain（基于entity_id前缀）\n", "    true_domain = eid.split(\".\", 1)[0]\n", "    print(f\"🔍 真实domain: {true_domain}\")\n", "    \n", "    # 3. 服务映射（基于真实domain）\n", "    service_map = SERVICE_MAP.get(true_domain, {})\n", "    service = service_map.get(raw_service, raw_service)\n", "    print(f\"🔍 服务映射: {raw_service} -> {service} (domain: {true_domain})\")\n", "    \n", "    # 4. 如果服务还是不对，根据用户意图强制修正\n", "    meta = HA_INDEX[\"by_entity\"].get(eid, {})\n", "    allowed_services = set(meta.get(\"services\", []))\n", "\n", "    if service not in allowed_services:\n", "        print(f\"🔍 服务不在白名单，尝试智能修正...\")\n", "        ut = (user_text or \"\").lower()\n", "        \n", "        # 通用的开关类设备\n", "        if \"turn_on\" in allowed_services and \"turn_off\" in allowed_services:\n", "            if any(k in ut for k in [\"关\", \"关闭\", \"off\", \"停止\"]):\n", "                service = \"turn_off\"\n", "            else:\n", "                service = \"turn_on\"  # 默认开启\n", "        \n", "        # 特殊设备的特殊处理\n", "        elif true_domain == \"cover\":\n", "            if any(k in ut for k in [\"开\", \"打开\", \"open\"]):\n", "                service = \"open_cover\"\n", "            elif any(k in ut for k in [\"关\", \"关闭\", \"close\"]):\n", "                service = \"close_cover\"\n", "            else:\n", "                service = \"open_cover\"\n", "                \n", "        elif true_domain == \"fan\":\n", "            if \"%\" in ut or \"百分比\" in ut or any(k in raw_args for k in [\"percentage\", \"value\"]):\n", "                service = \"set_percentage\"\n", "            elif \"turn_off\" in allowed_services:\n", "                service = \"turn_off\"\n", "            else:\n", "                service = \"turn_on\"\n", "                \n", "        elif true_domain == \"lock\":\n", "            if any(k in ut for k in [\"解锁\", \"unlock\", \"开锁\"]):\n", "                service = \"unlock\"\n", "            else:\n", "                service = \"lock\"\n", "                \n", "        elif true_domain == \"humidifier\":\n", "            if \"%\" in ut or \"湿度\" in ut:\n", "                service = \"set_humidity\"\n", "            elif \"turn_off\" in allowed_services:\n", "                service = \"turn_off\"\n", "            else:\n", "                service = \"turn_on\"\n", "                \n", "        elif true_domain == \"vacuum\":\n", "            if any(k in ut for k in [\"风速\", \"speed\", \"fan\"]):\n", "                service = \"set_fan_speed\"\n", "            elif any(k in ut for k in [\"暂停\", \"pause\"]):\n", "                service = \"pause\"\n", "            elif any(k in ut for k in [\"停止\", \"stop\"]):\n", "                service = \"stop\"\n", "            elif any(k in ut for k in [\"回充\", \"return\", \"dock\"]):\n", "                service = \"return_to_base\"\n", "            else:\n", "                service = \"start\"\n", "        \n", "        print(f\"🔍 智能修正后服务: {service}\")\n", "    \n", "    # 5. 最终验证\n", "    if service not in allowed_services:\n", "        raise SystemExit(f\"服务不被允许: {true_domain}.{service} for {eid}; 允许: {sorted(allowed_services)}\")\n", "    \n", "    # 6. 参数处理\n", "    args = _remap_args(true_domain, service, raw_args)\n", "    \n", "    # 参数白名单过滤\n", "    allowed_args = set(meta.get(\"args\", {}).get(service, []))\n", "    args = {k: v for k, v in args.items() if k in allowed_args}\n", "    \n", "    # 数值范围处理\n", "    if true_domain == \"light\" and service == \"turn_on\" and \"brightness_pct\" in args:\n", "        try:\n", "            args[\"brightness_pct\"] = max(0, min(100, int(args[\"brightness_pct\"])))\n", "        except:\n", "            args.pop(\"brightness_pct\", None)\n", "    \n", "    if true_domain == \"fan\" and service == \"set_percentage\" and \"percentage\" in args:\n", "        try:\n", "            args[\"percentage\"] = max(0, min(100, int(args[\"percentage\"])))\n", "        except:\n", "            raise SystemExit(\"fan.set_percentage 需要整数 percentage 0-100\")\n", "    \n", "    if true_domain == \"humidifier\" and service == \"set_humidity\" and \"humidity\" in args:\n", "        try:\n", "            args[\"humidity\"] = max(0, min(100, int(args[\"humidity\"])))\n", "        except:\n", "            raise SystemExit(\"humidifier.set_humidity 需要整数 humidity 0-100\")\n", "    \n", "    result = {\"domain\": true_domain, \"service\": service, \"entity_id\": eid, \"args\": args}\n", "    print(f\"🔍 最终结果: {result}\")\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "1676b399", "metadata": {}, "outputs": [], "source": ["INTENT_ALIAS_HINTS"]}, {"cell_type": "code", "execution_count": null, "id": "2f68080e", "metadata": {}, "outputs": [], "source": ["MODEL_PATH = \"/home/<USER>/LLM_Models/Qwen2.5-0.5B-Instruct-GGUF/qwen2.5-0.5b-instruct-fp16.gguf\"\n", "llm = Llama(model_path=MODEL_PATH, n_threads=8, n_ctx=2048)\n", "\n", "GBNF_JSON = r'''\n", "ws ::= [ \\t\\n\\r]*\n", "root ::= ws object ws\n", "object ::= \"{\" ws pair (ws \",\" ws pair)* ws \"}\"\n", "pair ::= string ws \":\" ws value\n", "string ::= \"\\\"\" [^\\\"]* \"\\\"\"\n", "value ::= string | number | object | array | \"true\" | \"false\" | \"null\"\n", "array ::= \"[\" ws (value (ws \",\" ws value)*)? ws \"]\"\n", "number ::= \"-\"? [0-9]+ (\".\" [0-9]+)?\n", "'''\n", "grammar_json = LlamaGrammar.from_string(GBNF_JSON)\n", "\n", "def format_chat_qwen(messages):\n", "    out = []\n", "    for m in messages:\n", "        out.append(f\"<|im_start|>{m['role']}\\n{m['content']}<|im_end|>\\n\")\n", "    out.append(\"<|im_start|>assistant\\n\")\n", "    return \"\".join(out)\n", "\n", "SYSTEM_PROMPT = (\n", "    \"你是 Home Assistant 控制器，只能输出 JSON：\"\n", "    '{\"domain\":\"...\",\"service\":\"...\",\"entity_id\":\"...\",\"args\":{...}}。\\n'\n", "    \"实体提示：\\n\" + build_entity_hints(HA_INDEX)\n", ")\n", "\n", "def llm_parse_to_json(user_text: str):\n", "    prompt = format_chat_qwen([\n", "        {\"role\":\"system\",\"content\": SYSTEM_PROMPT},\n", "        {\"role\":\"user\",\"content\": user_text}\n", "    ])\n", "    out = llm.create_completion(prompt=prompt, max_tokens=128, temperature=0, grammar=grammar_json, stop=[\"<|im_end|>\"])\n", "    raw = out[\"choices\"][0][\"text\"].strip()\n", "    print(\"LLM RAW:\", raw)\n", "    return json.loads(raw)\n", "\n", "def execute_nl(user_text: str, check_state: bool = True):\n", "    cmd_raw = llm_parse_to_json(user_text)   # 仍旧用你现在的 LLM + 简单 JSON 语法\n", "    # ★ 关键：把原句一起传入，用于启发式兜底\n", "    # fixed = normalize_cmd_with_text(cmd_raw, user_text=user_text)\n", "    fixed = normalize_cmd_with_text_v2(cmd_raw, user_text=user_text)\n", "\n", "    print(\"=== 规范化指令 ===\")\n", "    jprint(fixed)\n", "\n", "    # 打印等价 curl\n", "    body = {\"entity_id\": fixed[\"entity_id\"]}; body.update(fixed.get(\"args\") or {})\n", "    curl = (\n", "        f\"curl -X POST '{HA_URL}/api/services/{fixed['domain']}/{fixed['service']}' \"\n", "        \"-H 'Content-Type: application/json' \"\n", "        \"-H 'Authorization: Bearer $HA_TOKEN' \"\n", "        f\"-d '{json.dumps(body, ensure_ascii=False)}'\"\n", "    )\n", "    print(\"\\n=== 等价 curl ===\")\n", "    print(curl)\n", "\n", "    # 真正执行\n", "    resp = call_service(fixed[\"domain\"], fixed[\"service\"], fixed[\"entity_id\"], **(fixed.get(\"args\") or {}))\n", "    try:\n", "        print(\"\\n=== 返回 ===\")\n", "        jprint(resp)\n", "    except Exception:\n", "        print(resp)\n", "\n", "    if check_state:\n", "        try:\n", "            st = get_state(fixed[\"entity_id\"])\n", "            print(\"\\n=== 最新状态 ===\")\n", "            jprint(st)\n", "        except Exception as e:\n", "            print(f\"[WARN] 读取状态失败：{e}\")\n", "\n", "    return fixed, resp\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "46dd0aab", "metadata": {}, "outputs": [], "source": ["execute_nl(\"打开厨房的窗\")"]}, {"cell_type": "code", "execution_count": null, "id": "e56abefb", "metadata": {}, "outputs": [], "source": ["tests = [\n", "    \"把卧室灯开到 70%\",\n", "    \"把客厅风扇调到 50%\",\n", "    \"打开厨房窗户\",\n", "    \"锁上前门\",\n", "    \"加湿器设为 55%\",\n", "    \"扫地机调到 high 风速并开始\"\n", "]\n", "\n", "for t in tests:\n", "    print(\"\\n==============================\")\n", "    print(\"指令:\", t)\n", "    try:\n", "        execute_nl(t)\n", "    except SystemExit as e:\n", "        print(\"[ERROR]\", e)\n"]}, {"cell_type": "code", "execution_count": 32, "id": "8d3af4ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==============================\n", "指令: 打开客厅风扇\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Llama.generate: 309 prefix-match hit, remaining 6 prompt tokens to eval\n", "llama_perf_context_print:        load time =    2747.71 ms\n", "llama_perf_context_print: prompt eval time =     259.16 ms /     6 tokens (   43.19 ms per token,    23.15 tokens per second)\n", "llama_perf_context_print:        eval time =    2241.34 ms /    44 runs   (   50.94 ms per token,    19.63 tokens per second)\n", "llama_perf_context_print:       total time =    3080.36 ms /    50 tokens\n", "llama_perf_context_print:    graphs reused =         41\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LLM RAW: {\n", "  \"domain\": \"homeassistant\",\n", "  \"service\": \"fan.open\",\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"args\": {\n", "    \"on\": true\n", "  }\n", "}\n", "🔍 输入: domain=homeassistant, service=fan.open, entity_id=fan.living_room_fan\n", "🔍 直接匹配成功: fan.living_room_fan\n", "🔍 真实domain: fan\n", "🔍 服务映射: fan.open -> fan.open (domain: fan)\n", "🔍 服务不在白名单，尝试智能修正...\n", "🔍 智能修正后服务: turn_on\n", "🔍 最终结果: {'domain': 'fan', 'service': 'turn_on', 'entity_id': 'fan.living_room_fan', 'args': {}}\n", "=== 规范化指令 ===\n", "{\n", "  \"domain\": \"fan\",\n", "  \"service\": \"turn_on\",\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"args\": {}\n", "}\n", "\n", "=== 等价 curl ===\n", "curl -X POST 'http://**************:8123/api/services/fan/turn_on' -H 'Content-Type: application/json' -H 'Authorization: Bearer $HA_TOKEN' -d '{\"entity_id\": \"fan.living_room_fan\"}'\n", "\n", "=== 返回 ===\n", "[\n", "  {\n", "    \"entity_id\": \"fan.living_room_fan\",\n", "    \"state\": \"on\",\n", "    \"attributes\": {\n", "      \"preset_modes\": [\n", "        \"auto\",\n", "        \"smart\",\n", "        \"sleep\",\n", "        \"on\"\n", "      ],\n", "      \"direction\": \"forward\",\n", "      \"oscillating\": false,\n", "      \"percentage\": 67,\n", "      \"percentage_step\": 33.333333333333336,\n", "      \"preset_mode\": null,\n", "      \"friendly_name\": \"Living Room Fan\",\n", "      \"supported_features\": 55\n", "    },\n", "    \"last_changed\": \"2025-09-16T09:03:07.175071+00:00\",\n", "    \"last_reported\": \"2025-09-16T09:12:16.333545+00:00\",\n", "    \"last_updated\": \"2025-09-16T09:12:16.333545+00:00\",\n", "    \"context\": {\n", "      \"id\": \"01K58WQMPCHXAFK5NQZG59W605\",\n", "      \"parent_id\": null,\n", "      \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "    }\n", "  }\n", "]\n", "\n", "=== 最新状态 ===\n", "{\n", "  \"entity_id\": \"fan.living_room_fan\",\n", "  \"state\": \"on\",\n", "  \"attributes\": {\n", "    \"preset_modes\": [\n", "      \"auto\",\n", "      \"smart\",\n", "      \"sleep\",\n", "      \"on\"\n", "    ],\n", "    \"direction\": \"forward\",\n", "    \"oscillating\": false,\n", "    \"percentage\": 67,\n", "    \"percentage_step\": 33.333333333333336,\n", "    \"preset_mode\": null,\n", "    \"friendly_name\": \"Living Room Fan\",\n", "    \"supported_features\": 55\n", "  },\n", "  \"last_changed\": \"2025-09-16T09:03:07.175071+00:00\",\n", "  \"last_reported\": \"2025-09-16T09:12:16.333545+00:00\",\n", "  \"last_updated\": \"2025-09-16T09:12:16.333545+00:00\",\n", "  \"context\": {\n", "    \"id\": \"01K58WQMPCHXAFK5NQZG59W605\",\n", "    \"parent_id\": null,\n", "    \"user_id\": \"0d15b6d598cb4f518960ec91c54b81b4\"\n", "  }\n", "}\n"]}], "source": ["tests = [\n", "    \"打开客厅风扇\",\n", "]\n", "\n", "for t in tests:\n", "    print(\"\\n==============================\")\n", "    print(\"指令:\", t)\n", "    try:\n", "        execute_nl(t)\n", "    except SystemExit as e:\n", "        print(\"[ERROR]\", e)\n"]}], "metadata": {"kernelspec": {"display_name": "llamacpp", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}